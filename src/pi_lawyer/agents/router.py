"""
Router for LangGraph Agents

This module implements the router logic for LangGraph agents.
It determines which agent should handle a request based on the state.
"""

import logging
from typing import Any, Dict

from ..state.state import AiLexState

# Set up logging
logger = logging.getLogger(__name__)

# Define the available agents
AVAILABLE_AGENTS = {
    "intake_agent": "Intake Agent for new matters",
    "research_agent": "Research Agent for legal research",
    "document_agent": "Document Agent for document analysis",
    "deadline_agent": "Deadline Agent for deadline management",
    "calendar_agent": "Calendar Agent for managing calendar events",
    "case_client_agent": "Case and Client Agent for managing cases and clients",
    "supervisor_agent": "Supervisor Agent for coordinating other agents",
}

def route_request(state: AiLexState) -> str:
    """
    Route a request to the appropriate agent based on the state.

    Args:
        state: The current state

    Returns:
        The name of the agent to handle the request
    """
    logger.info(f"Routing request for thread {state.thread_id}")

    # If there's an active agent in memory, use it
    active_agent = state.get_active_agent()
    if active_agent and active_agent in AVAILABLE_AGENTS:
        logger.info(f"Using active agent from memory: {active_agent}")
        return active_agent

    # If there's a matter_id, route based on the last message
    if state.matter_id:
        last_message = state.get_last_message()
        if last_message:
            content = last_message.get("content", "")
            if isinstance(content, list):
                content = " ".join([str(c) for c in content])

            # Simple keyword-based routing
            if "research" in content.lower() or "find" in content.lower() or "search" in content.lower():
                logger.info("Routing to research_agent based on keywords")
                return "research_agent"

            if "document" in content.lower() or "draft" in content.lower() or "write" in content.lower():
                logger.info("Routing to document_agent based on keywords")
                return "document_agent"

            if "deadline" in content.lower() or "due date" in content.lower():
                logger.info("Routing to deadline_agent based on keywords")
                return "deadline_agent"

            # Calendar-related keywords
            calendar_keywords = ["calendar", "schedule", "appointment", "meeting", "event", "book", "reserve"]
            if any(keyword in content.lower() for keyword in calendar_keywords):
                logger.info("Routing to calendar_agent based on keywords")
                return "calendar_agent"

            # Case and client-related keywords
            case_keywords = ["case", "matter", "lawsuit", "litigation"]
            client_keywords = ["client", "plaintiff", "defendant", "party"]
            if any(keyword in content.lower() for keyword in case_keywords) or \
               any(keyword in content.lower() for keyword in client_keywords) or \
               content.lower().startswith("case.") or \
               content.lower().startswith("client."):
                logger.info("Routing to case_client_agent based on keywords")
                return "case_client_agent"

    # If no matter_id, route to intake agent
    if not state.matter_id:
        logger.info("No matter_id, routing to intake_agent")
        return "intake_agent"

    # Default to supervisor agent
    logger.info("Using default supervisor_agent")
    return "supervisor_agent"

def get_agent_description(agent_name: str) -> str:
    """
    Get the description of an agent.

    Args:
        agent_name: The name of the agent

    Returns:
        The description of the agent
    """
    return AVAILABLE_AGENTS.get(agent_name, "Unknown agent")

def analyze_request(
    state: AiLexState
) -> Dict[str, Any]:
    """
    Analyze a request to determine routing and context.

    Args:
        state: The current state

    Returns:
        A dictionary with routing information and context
    """
    # Determine the agent to route to
    agent_name = route_request(state)

    # Get the agent description
    agent_description = get_agent_description(agent_name)

    # Extract context from the state
    context = {
        "tenant_id": state.tenant_id,
        "user_id": state.user_id,
        "thread_id": state.thread_id,
        "matter_id": state.matter_id,
        "locale": state.locale,
        "active_doc": state.active_doc,
        "message_count": len(state.messages),
        "has_user_messages": len(state.get_messages_by_role("user")) > 0,
        "has_assistant_messages": len(state.get_messages_by_role("assistant")) > 0,
    }

    # Create the routing information
    routing_info = {
        "agent": agent_name,
        "agent_description": agent_description,
        "context": context,
        "timestamp": state.updated_at,
    }

    logger.info(f"Request analysis: {routing_info}")
    return routing_info
