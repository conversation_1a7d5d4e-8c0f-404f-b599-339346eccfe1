"""
CopilotKit Runtime Configuration

This module configures the CopilotKit runtime for the PI Lawyer AI application.
It sets up the FastAPI server and integrates with LangGraph for agent execution.
"""

# Load environment variables first, before any other imports
import os
from pathlib import Path

from dotenv import load_dotenv

# Find and load .env file explicitly
env_path = Path(__file__).parent.parent.parent.parent / '.env'
print(f"Loading environment variables from: {env_path}")

# Parse .env file and set environment variables
if env_path.exists():
    # Load .env and override any existing vars to pick up updated values
    load_dotenv(dotenv_path=env_path, override=True)
    print("Environment variables loaded successfully")

    # Print loaded environment variables (excluding sensitive values)
    print("Loaded environment variables:")
    for key, value in os.environ.items():
        if key.startswith(('VOYAGE_', 'OPENAI_', 'PINECONE_', 'SUPABASE_', 'CPK_')):
            if any(sensitive in key.lower() for sensitive in ['key', 'password', 'secret', 'token']):
                print(f"  {key}=****")
            else:
                print(f"  {key}={value}")
else:
    print(f"Warning: .env file not found at {env_path}")

# Now import other modules after environment variables are loaded
import logging
import secrets

import uvicorn
from copilotkit import CopilotKitSDK as CopilotKitRemoteEndpoint
from copilotkit import LangGraphAgent
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from ..agents.deadline_agent import app as deadline_graph
from ..agents.document_agent import app as document_graph
from ..agents.event_agent import app as event_graph
from ..agents.intake_agent import app as intake_graph
# Import research agent from backend location
from backend.agents.interactive.research.agent import ResearchAgent
from ..middleware.jwt_middleware import verify_jwt_middleware

# Create research graph from backend agent
research_agent = ResearchAgent()
research_graph = research_agent.create_graph()

# Import the supervisor agent from backend location
try:
    from backend.agents.insights.supervisor.agent import SupervisorAgent
    supervisor_agent = SupervisorAgent()
    supervisor_graph = supervisor_agent.create_graph()
except ImportError as e:
    # If supervisor agent not yet available, provide a placeholder
    from langgraph.checkpoint.memory import MemorySaver
    logger.warning(f"Failed to import supervisor agent: {e}")
    supervisor_graph = {"graph": None, "memory": MemorySaver()}

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("pi_lawyer.api")

# Validate security configuration at startup
try:
    from ..security import validate_startup_security
    validate_startup_security()
    logger.info("✅ Security validation passed - starting application")
except Exception as e:
    logger.error(f"❌ Security validation failed: {e}")
    logger.error("Application startup blocked due to security configuration issues")
    raise

app = FastAPI()

# Enhanced CORS setup with configuration from environment variables
from config import settings

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Authorization", "Content-Type", "X-CPK-Endpoint-Key", "X-Client-Info"],
    expose_headers=["Content-Length", "Content-Range"],
    max_age=86400,  # 24 hours
)

# Add security middleware
from ..middleware.rate_limit_middleware import rate_limit_middleware
from ..middleware.security_headers_middleware import security_headers_middleware

# Add middleware in the correct order (outermost to innermost)
app.middleware("http")(security_headers_middleware)  # Applied last (outermost)
app.middleware("http")(rate_limit_middleware)        # Applied second
app.middleware("http")(verify_jwt_middleware)        # Applied first (innermost)

# CopilotKit Endpoint Key Verification Middleware
@app.middleware("http")
async def verify_endpoint_key(request: Request, call_next):
    """
    Middleware to verify the CopilotKit endpoint key for secure access.
    This protects the /copilotkit endpoint used by CopilotKit Cloud.

    Args:
        request: The FastAPI request object
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler
    """
    # Only check auth for /copilotkit path, but exclude health check and OPTIONS requests
    if (request.url.path.startswith("/copilotkit") and
        not request.url.path.endswith("/health") and
        request.method != "OPTIONS"):

        # Get the endpoint secret from settings
        endpoint_secret = settings.copilotkit.endpoint_secret

        # If no secret is set or in development mode with default secret, disable auth
        if not endpoint_secret or (endpoint_secret == "test-endpoint-secret-123456789" and settings.debug):
            logger.warning(
                "CopilotKit endpoint authentication disabled! "
                "This is insecure and should only be used in development."
            )
            request.state.cpk_auth_disabled = True
        else:
            # Check the header for the endpoint key
            auth_header = request.headers.get("X-CPK-Endpoint-Key")

            # Log attempt with masked key for security
            masked_header = "None" if not auth_header else f"{auth_header[:4]}...{auth_header[-4:]}" if len(auth_header) > 8 else "********"
            logger.debug(f"CopilotKit endpoint key verification attempt with key: {masked_header}")

            if not auth_header:
                logger.warning("No CopilotKit endpoint key provided")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "CopilotKit endpoint key required"}
                )

            # Use constant-time comparison to prevent timing attacks
            if not secrets.compare_digest(auth_header, endpoint_secret):
                logger.warning("Invalid CopilotKit endpoint key provided")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Invalid CopilotKit endpoint key"}
                )

            # Store authentication status in request state
            request.state.cpk_authenticated = True
            logger.debug("CopilotKit endpoint key verification successful")

    # Proceed with the request
    response = await call_next(request)
    return response

# Import route modules
from .routes.copilotkit_endpoint import router as copilotkit_endpoint_router
from .routes.deadline_extraction import router as deadline_router

# Include routers
app.include_router(deadline_router, prefix="/api")
app.include_router(copilotkit_endpoint_router)

sdk = CopilotKitRemoteEndpoint(
    agents=[
        LangGraphAgent(
            name="supervisor_agent",
            description="Legal assistant supervisor that routes to specialized agents",
            graph=supervisor_graph,  # the supervisor agent graph
        ),
        LangGraphAgent(
            name="intake_agent",
            description="Legal intake assistant for personal injury cases",
            graph=intake_graph,  # the intake agent graph
        ),
        LangGraphAgent(
            name="deadline_agent",
            description="Legal deadline extraction assistant for identifying important deadlines in legal documents",
            graph=deadline_graph,  # the deadline agent graph
        ),
        LangGraphAgent(
            name="document_agent",
            description="Legal document drafting assistant for personal injury cases",
            graph=document_graph,  # the document drafting agent graph
        ),
        LangGraphAgent(
            name="research_agent",
            description="Legal research assistant for finding relevant laws and cases",
            graph=research_graph,  # the legal research agent graph
        ),
        LangGraphAgent(
            name="event_agent",
            description="Event details extraction assistant for legal cases",
            graph=event_graph,  # the event extraction agent graph
        ),
    ],
)

# We're now using our custom CopilotKit endpoint with AG-UI protocol support
# The endpoint is registered via the copilotkit_endpoint_router above
# add_fastapi_endpoint(app, sdk, "/copilotkit", use_thread_pool=False)

# Import and register API routes
from .document_route import router as document_router
from .intake_route import router as intake_router
from .research_route import router as research_router
from .routes.document_analysis import router as document_analysis_router
from .task_route import router as task_router

# Include the routers in the app
app.include_router(intake_router)
app.include_router(document_router)
app.include_router(task_router)
app.include_router(research_router)
app.include_router(document_analysis_router, prefix="/document-analysis")


# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring and container health checks.

    Returns:
        dict: Health status information including version, uptime, and component status
    """
    import platform
    import time
    from datetime import datetime

    # Get application start time (or use current time if not available)
    start_time = getattr(app, "start_time", time.time())
    uptime_seconds = int(time.time() - start_time)

    # Basic system information
    system_info = {
        "python_version": platform.python_version(),
        "system": platform.system(),
        "platform": platform.platform(),
    }

    # Check components - add more component checks as needed
    components = {
        "api": {"status": "healthy", "details": "API is responding"},
    }

    # Add database check if available
    try:
        # Simple check - this could be expanded to actually test DB connectivity
        if os.environ.get("DB_HOST"):
            components["database"] = {"status": "configured", "host": os.environ.get("DB_HOST")}
    except Exception as e:
        components["database"] = {"status": "error", "details": str(e)}

    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": os.environ.get("APP_ENV", "development"),
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": uptime_seconds,
        "system_info": system_info,
        "components": components
    }


def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "pi_lawyer.api.runtime:app",  # the path to your FastAPI file, replace this if its different
        host="0.0.0.0",
        port=port,
        reload=True,
    )


if __name__ == "__main__":
    main()
