"""
Enhanced Pydantic models for API validation.

This module provides comprehensive validation models for common API patterns
with built-in security and data integrity checks.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, validator

from .decorators import EnhancedBaseModel, SecureStringField, SecureIntegerField


class PaginationRequest(EnhancedBaseModel):
    """Standard pagination request model."""
    
    page: int = SecureIntegerField.positive(default=1, description="Page number (1-based)")
    limit: int = SecureIntegerField.create(
        min_value=1, 
        max_value=100, 
        default=20, 
        description="Number of items per page (1-100)"
    )
    sort_by: Optional[str] = SecureStringField.create(
        max_length=50,
        regex=r'^[a-zA-Z_][a-zA-Z0-9_]*$',
        description="Field to sort by"
    )
    sort_order: Optional[str] = Field(
        default="asc",
        regex=r'^(asc|desc)$',
        description="Sort order: asc or desc"
    )


class SearchRequest(EnhancedBaseModel):
    """Standard search request model."""
    
    query: str = SecureStringField.create(
        min_length=1,
        max_length=500,
        description="Search query"
    )
    filters: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional search filters"
    )
    pagination: Optional[PaginationRequest] = Field(
        default_factory=PaginationRequest,
        description="Pagination parameters"
    )
    
    @validator('filters')
    def validate_filters(cls, v):
        """Validate search filters."""
        if not isinstance(v, dict):
            raise ValueError("Filters must be a dictionary")
        
        # Limit number of filter keys
        if len(v) > 20:
            raise ValueError("Too many filter keys (maximum 20)")
        
        # Validate filter keys
        for key in v.keys():
            if not isinstance(key, str):
                raise ValueError("Filter keys must be strings")
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                raise ValueError(f"Invalid filter key format: {key}")
        
        return v


class UserIdentification(EnhancedBaseModel):
    """User identification model."""
    
    user_id: UUID = Field(..., description="User UUID")
    tenant_id: UUID = Field(..., description="Tenant UUID")
    email: Optional[str] = SecureStringField.email(description="User email")
    role: str = Field(
        ...,
        regex=r'^(user|admin|partner|superadmin)$',
        description="User role"
    )


class DocumentMetadata(EnhancedBaseModel):
    """Document metadata model."""
    
    filename: str = SecureStringField.create(
        max_length=255,
        regex=r'^[a-zA-Z0-9\s\-_.()]+\.[a-zA-Z0-9]+$',
        description="Document filename with extension"
    )
    content_type: str = Field(
        ...,
        regex=r'^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$',
        description="MIME content type"
    )
    size: int = SecureIntegerField.create(
        min_value=1,
        max_value=100 * 1024 * 1024,  # 100MB
        description="File size in bytes"
    )
    checksum: Optional[str] = Field(
        None,
        regex=r'^[a-fA-F0-9]{32,128}$',
        description="File checksum (MD5, SHA256, etc.)"
    )
    
    @validator('content_type')
    def validate_content_type(cls, v):
        """Validate content type against allowed types."""
        allowed_types = {
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/rtf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/tiff',
            'audio/mpeg',
            'audio/wav',
            'audio/x-m4a',
            'audio/ogg',
            'video/mp4',
            'video/avi',
            'video/quicktime',
            'video/x-ms-wmv'
        }
        
        if v not in allowed_types:
            raise ValueError(f"Unsupported content type: {v}")
        
        return v


class ContactInformation(EnhancedBaseModel):
    """Contact information model."""
    
    first_name: str = SecureStringField.name(description="First name")
    last_name: str = SecureStringField.name(description="Last name")
    middle_name: Optional[str] = SecureStringField.name(description="Middle name")
    email: Optional[str] = SecureStringField.email(description="Email address")
    phone: Optional[str] = SecureStringField.phone(description="Phone number")
    address: Optional[str] = SecureStringField.create(
        max_length=500,
        description="Street address"
    )
    city: Optional[str] = SecureStringField.name(description="City")
    state: Optional[str] = Field(
        None,
        regex=r'^[A-Z]{2}$',
        description="Two-letter state code"
    )
    zip_code: Optional[str] = Field(
        None,
        regex=r'^\d{5}(-\d{4})?$',
        description="ZIP code"
    )


class MatterInformation(EnhancedBaseModel):
    """Matter/case information model."""
    
    practice_area: str = Field(
        ...,
        regex=r'^(personal_injury|intellectual_property|criminal_defense|family_law|business_law)$',
        description="Practice area"
    )
    work_type: str = Field(
        ...,
        regex=r'^(litigation|transactional|consultation|compliance)$',
        description="Type of work"
    )
    matter_description: str = SecureStringField.create(
        min_length=10,
        max_length=5000,
        description="Detailed matter description"
    )
    priority: str = Field(
        default="medium",
        regex=r'^(low|medium|high|urgent)$',
        description="Matter priority level"
    )
    date_of_incident: Optional[datetime] = Field(
        None,
        description="Date of incident (if applicable)"
    )
    statute_of_limitations: Optional[datetime] = Field(
        None,
        description="Statute of limitations deadline"
    )


class IntakeRequest(EnhancedBaseModel):
    """Comprehensive intake request model."""
    
    client_info: ContactInformation = Field(..., description="Client contact information")
    matter_info: MatterInformation = Field(..., description="Matter details")
    referral_source: Optional[str] = SecureStringField.create(
        max_length=200,
        description="How the client found the firm"
    )
    previous_attorney: bool = Field(
        default=False,
        description="Whether client has consulted another attorney"
    )
    previous_attorney_details: Optional[str] = SecureStringField.create(
        max_length=1000,
        description="Details about previous attorney consultation"
    )
    additional_notes: Optional[str] = SecureStringField.create(
        max_length=2000,
        description="Additional notes or comments"
    )
    
    @validator('previous_attorney_details')
    def validate_previous_attorney_details(cls, v, values):
        """Validate previous attorney details are provided when needed."""
        if values.get('previous_attorney') and not v:
            raise ValueError("Previous attorney details required when previous_attorney is True")
        return v


class DocumentUploadRequest(EnhancedBaseModel):
    """Document upload request model."""
    
    matter_id: UUID = Field(..., description="Associated matter UUID")
    document_type: str = Field(
        ...,
        regex=r'^(contract|correspondence|evidence|pleading|discovery|other)$',
        description="Type of document"
    )
    processing_path: str = Field(
        default="auto",
        regex=r'^(auto|simple|complex)$',
        description="Processing path preference"
    )
    metadata: DocumentMetadata = Field(..., description="Document metadata")
    tags: Optional[List[str]] = Field(
        default_factory=list,
        max_items=20,
        description="Document tags"
    )
    
    @validator('tags')
    def validate_tags(cls, v):
        """Validate document tags."""
        if not isinstance(v, list):
            raise ValueError("Tags must be a list")
        
        for tag in v:
            if not isinstance(tag, str):
                raise ValueError("All tags must be strings")
            if len(tag) > 50:
                raise ValueError("Tag length cannot exceed 50 characters")
            if not re.match(r'^[a-zA-Z0-9\s\-_]+$', tag):
                raise ValueError(f"Invalid tag format: {tag}")
        
        return v


class ValidationActionRequest(EnhancedBaseModel):
    """Validation action request model."""
    
    action: str = Field(
        ...,
        regex=r'^(validate|reject|approve|deny)$',
        description="Action to take"
    )
    note: Optional[str] = SecureStringField.create(
        max_length=1000,
        description="Optional note about the action"
    )
    reviewer_id: Optional[UUID] = Field(
        None,
        description="ID of the reviewing user"
    )
