"""
Central Pytest Configuration for Agent System

This module provides comprehensive pytest configuration for all agent tests,
including fixtures, plugins, and utilities for testing the entire agent system.

Key Features:
- Unified test configuration across all agents
- Mock external dependencies (Supabase, Pinecone, LLMs)
- State management fixtures
- Performance testing utilities
- Database session management
- Tenant isolation testing
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker

# Configure logging for tests
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


# ============================================================================
# Environment and Configuration Fixtures
# ============================================================================

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up test environment variables and configuration."""
    # Save original environment
    original_env = os.environ.copy()
    
    # Set test environment variables
    test_env = {
        "TESTING": "true",
        "LOG_LEVEL": "DEBUG",
        "VOYAGE_API_KEY": "test-voyage-api-key",
        "PINECONE_API_KEY": "test-pinecone-api-key",
        "PINECONE_ENVIRONMENT": "test-environment",
        "PINECONE_INDEX_NAME": "test-index",
        "OPENAI_API_KEY": "test-openai-api-key",
        "SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_KEY": "test-supabase-key",
        "SUPABASE_JWT_SECRET": "test-jwt-secret",
        "CPK_ENDPOINT_SECRET": "test-endpoint-secret",
        "TEST_DATABASE_URL": "sqlite:///:memory:",
        "APP_ENV": "test",
    }
    
    for key, value in test_env.items():
        os.environ[key] = value
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


# ============================================================================
# Mock External Dependencies
# ============================================================================

@pytest.fixture(autouse=True)
def mock_external_apis():
    """Mock all external API dependencies."""
    mocks = {}
    patches = []

    # List of modules to mock with their paths
    mock_targets = [
        ("voyage", "shared.core.llm.voyage.VoyageEmbeddings"),
        ("pinecone", "pinecone.Pinecone"),
        ("openai", "openai.OpenAI"),
        ("supabase", "shared.core.db.supabase.SupabaseClient"),
    ]

    # Create patches for each target, handling missing modules gracefully
    for name, target in mock_targets:
        try:
            patch_obj = patch(target, create=True)
            mock_obj = patch_obj.start()
            patches.append(patch_obj)
            mocks[name] = mock_obj
        except (ImportError, AttributeError):
            # Create a simple MagicMock if the module doesn't exist
            mocks[name] = MagicMock()

    # Configure mocks with realistic behavior
    if "voyage" in mocks:
        mock_voyage_instance = MagicMock()
        mock_voyage_instance.embed_query.return_value = [0.1] * 1536
        mock_voyage_instance.embed_documents.return_value = [[0.1] * 1536]
        mocks["voyage"].return_value = mock_voyage_instance

    if "pinecone" in mocks:
        mock_index = MagicMock()
        mock_index.query.return_value = {
            "matches": [
                {"id": "doc1", "score": 0.9, "metadata": {"text": "Test document 1"}},
                {"id": "doc2", "score": 0.8, "metadata": {"text": "Test document 2"}},
            ]
        }
        mock_pinecone_instance = MagicMock()
        mock_pinecone_instance.Index.return_value = mock_index
        mocks["pinecone"].return_value = mock_pinecone_instance

    if "openai" in mocks:
        mock_openai_instance = MagicMock()
        mock_completion = MagicMock()
        mock_completion.choices = [MagicMock(message=MagicMock(content="Test response"))]
        mock_openai_instance.chat.completions.create.return_value = mock_completion
        mocks["openai"].return_value = mock_openai_instance

    if "supabase" in mocks:
        mock_supabase_instance = MagicMock()
        mock_supabase_instance.get_user.return_value = {"id": "test-user-id", "email": "<EMAIL>"}
        mock_supabase_instance.get_tenant.return_value = {"id": "test-tenant-id", "name": "Test Tenant"}
        mocks["supabase"].return_value = mock_supabase_instance

    yield mocks

    # Clean up patches
    for patch_obj in patches:
        try:
            patch_obj.stop()
        except RuntimeError:
            pass  # Patch was already stopped


# ============================================================================
# Database and Session Management
# ============================================================================

@pytest.fixture(scope="function")
def db_session():
    """
    Provide a clean database session for each test.
    
    This fixture creates an in-memory SQLite database, sets up tables,
    and provides a session that is automatically cleaned up after each test.
    """
    # Create in-memory SQLite engine
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,  # Set to True for SQL debugging
    )
    
    # Enable foreign key constraints for SQLite
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()
    
    # Create session factory
    session_factory = sessionmaker(bind=engine)
    session = session_factory()
    
    try:
        yield session
    finally:
        session.close()
        engine.dispose()


# ============================================================================
# Agent Testing Fixtures
# ============================================================================

@pytest.fixture
def tenant_id():
    """Generate a unique tenant ID for testing."""
    return f"tenant-{uuid.uuid4()}"


@pytest.fixture
def user_id():
    """Generate a unique user ID for testing."""
    return f"user-{uuid.uuid4()}"


@pytest.fixture
def thread_id():
    """Generate a unique thread ID for testing."""
    return f"thread-{uuid.uuid4()}"


@pytest.fixture
def base_state(tenant_id, user_id, thread_id):
    """Create a base state for agent testing."""
    return {
        "messages": [],
        "tenant_id": tenant_id,
        "user_id": user_id,
        "user_context": {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "role": "attorney",
            "assigned_case_ids": [],
            "settings": {}
        },
        "thread_id": thread_id,
        "agent_type": "test",
        "memory": {},
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }


@pytest.fixture
def runnable_config(tenant_id, user_id, thread_id):
    """Create a runnable configuration for LangGraph testing."""
    return {
        "configurable": {
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": thread_id
        }
    }


# ============================================================================
# Mock Agent Fixtures
# ============================================================================

@pytest.fixture
def mock_base_agent():
    """Create a mock base agent for testing."""
    from shared.core.base_agent import BaseAgent, AgentConfig

    class MockAgent(BaseAgent):
        async def initialize(self, state):
            state.add_message("system", "Agent initialized")
            return state

        async def execute(self, state):
            state.add_message("assistant", "Agent executed")
            return state

        async def cleanup(self, state):
            state.add_message("system", "Agent cleaned up")
            return state

    config = AgentConfig(name="MockAgent", description="Test agent")
    return MockAgent(config)


@pytest.fixture
def mock_tool_executor():
    """Create a mock tool executor for testing."""
    executor = AsyncMock()
    executor.execute_tool.return_value = {"result": "success"}
    return executor


# ============================================================================
# Advanced Testing Fixtures
# ============================================================================

@pytest.fixture
def test_metrics_collector():
    """Create a test metrics collector for performance monitoring."""
    class TestMetricsCollector:
        def __init__(self):
            self.metrics = {}
            self.start_times = {}

        def start_timer(self, name: str):
            import time
            self.start_times[name] = time.time()

        def end_timer(self, name: str):
            import time
            if name in self.start_times:
                duration = time.time() - self.start_times[name]
                self.metrics[name] = duration
                del self.start_times[name]
                return duration
            return 0

        def record_metric(self, name: str, value: Any):
            self.metrics[name] = value

        def get_metrics(self):
            return self.metrics.copy()

    return TestMetricsCollector()


@pytest.fixture
def error_injection():
    """Create an error injection utility for testing error handling."""
    class ErrorInjector:
        def __init__(self):
            self.error_configs = {}

        def configure_error(self, target: str, error_type: type, error_message: str, trigger_count: int = 1):
            self.error_configs[target] = {
                "error_type": error_type,
                "error_message": error_message,
                "trigger_count": trigger_count,
                "current_count": 0
            }

        def should_raise_error(self, target: str) -> bool:
            if target in self.error_configs:
                config = self.error_configs[target]
                config["current_count"] += 1
                if config["current_count"] >= config["trigger_count"]:
                    return True
            return False

        def get_error(self, target: str) -> Exception:
            if target in self.error_configs:
                config = self.error_configs[target]
                return config["error_type"](config["error_message"])
            return Exception("Unknown error")

    return ErrorInjector()


@pytest.fixture
def test_data_generator():
    """Create a test data generator for creating realistic test data."""
    class TestDataGenerator:
        def __init__(self):
            self.seed = 42
            import random
            random.seed(self.seed)

        def generate_user_data(self, count: int = 1):
            import uuid
            users = []
            for i in range(count):
                users.append({
                    "id": f"user-{uuid.uuid4()}",
                    "email": f"user{i}@example.com",
                    "name": f"Test User {i}",
                    "role": "attorney" if i % 2 == 0 else "paralegal",
                    "tenant_id": f"tenant-{i // 5}",  # Group users by tenant
                    "created_at": datetime.utcnow().isoformat()
                })
            return users if count > 1 else users[0]

        def generate_case_data(self, count: int = 1):
            import uuid
            import random
            statuses = ["active", "pending", "closed", "archived"]
            practice_areas = ["personal_injury", "family_law", "criminal_defense", "corporate"]

            cases = []
            for i in range(count):
                cases.append({
                    "id": f"case-{uuid.uuid4()}",
                    "title": f"Test Case {i}",
                    "description": f"Description for test case {i}",
                    "status": random.choice(statuses),
                    "practice_area": random.choice(practice_areas),
                    "client_id": f"client-{i}",
                    "attorney_id": f"attorney-{i}",
                    "estimated_value": random.randint(1000, 100000),
                    "created_at": (datetime.utcnow() - timedelta(days=random.randint(1, 365))).isoformat()
                })
            return cases if count > 1 else cases[0]

        def generate_message_thread(self, length: int = 5):
            messages = []
            for i in range(length):
                role = "user" if i % 2 == 0 else "assistant"
                content = f"Test message {i} from {role}"
                messages.append({
                    "role": role,
                    "content": content,
                    "timestamp": (datetime.utcnow() - timedelta(minutes=length-i)).isoformat()
                })
            return messages

    return TestDataGenerator()
