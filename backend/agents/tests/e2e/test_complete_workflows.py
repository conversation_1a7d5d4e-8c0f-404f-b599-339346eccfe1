"""
End-to-end tests for complete agent workflows.

This module tests complete user scenarios from start to finish,
including all agent interactions, state management, and external services.
"""

import pytest
from unittest.mock import patch

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    AgentFactory,
    AgentAssertions,
    MockManager,
    TestDataManager,
    PerformanceTester
)


class TestCompleteWorkflows(BaseAgentTest):
    """Test complete end-to-end agent workflows."""
    
    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()
        self.test_data_manager = TestDataManager("test_data")
        self.performance_tester = PerformanceTester()
    
    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()
    
    @pytest.mark.e2e
    async def test_legal_research_workflow(self):
        """Test complete legal research workflow."""
        # Create research agent
        research_agent = self.mock_manager.create_mock_agent("ResearchAgent")
        
        # Mock external services
        mock_pinecone = self.mock_manager.create_mock_pinecone_client()
        mock_openai = self.mock_manager.create_mock_openai_client()
        
        with patch('pinecone.Pinecone', return_value=mock_pinecone), \
             patch('openai.OpenAI', return_value=mock_openai):
            
            # Create initial state with user query
            state = StateFactory.create_empty_state()
            state.add_message("user", "What is the statute of limitations for personal injury in Texas?")
            
            # Execute research workflow
            result, metrics = await self.performance_tester.measure_async_operation(
                self._execute_research_workflow,
                research_agent,
                state
            )
            
            # Verify workflow completion
            AgentAssertions.assert_state_valid(result)
            AgentAssertions.assert_message_contains(result, "statute of limitations")
            AgentAssertions.assert_agent_lifecycle_completed(research_agent)
            
            # Verify performance
            assert metrics.execution_time < 5.0
            assert metrics.success_rate == 1.0
    
    @pytest.mark.e2e
    async def test_document_analysis_workflow(self):
        """Test complete document analysis workflow."""
        # Create document agent
        document_agent = self.mock_manager.create_mock_agent("DocumentAgent")
        
        # Mock tool executor for document processing
        tool_responses = {
            "document_loader": {
                "content": "This is a legal document about personal injury claims...",
                "metadata": {"pages": 10, "type": "pdf"}
            },
            "document_analyzer": {
                "summary": "Document discusses personal injury claim procedures",
                "key_points": ["Filing deadlines", "Required documentation", "Compensation types"],
                "entities": ["Texas", "Personal Injury", "Statute of Limitations"]
            }
        }
        
        mock_executor = self.mock_manager.create_mock_tool_executor(tool_responses)
        
        with patch('shared.core.tools.executor.ToolExecutor', return_value=mock_executor):
            # Create initial state with document request
            state = StateFactory.create_empty_state()
            state.add_message("user", "Please analyze document doc-123 for key legal points")
            
            # Execute document analysis workflow
            result, metrics = await self.performance_tester.measure_async_operation(
                self._execute_document_workflow,
                document_agent,
                state,
                mock_executor
            )
            
            # Verify workflow completion
            AgentAssertions.assert_state_valid(result)
            AgentAssertions.assert_message_contains(result, "analysis")
            AgentAssertions.assert_tool_executed(mock_executor, "document_loader", 1)
            AgentAssertions.assert_tool_executed(mock_executor, "document_analyzer", 1)
            
            # Verify performance
            assert metrics.execution_time < 10.0
            assert metrics.success_rate == 1.0
    
    @pytest.mark.e2e
    async def test_case_management_workflow(self):
        """Test complete case management workflow."""
        # Create multiple agents for case management
        intake_agent = self.mock_manager.create_mock_agent("IntakeAgent")
        task_agent = self.mock_manager.create_mock_agent("TaskAgent")
        calendar_agent = self.mock_manager.create_mock_agent("CalendarAgent")
        
        # Mock database and external services
        mock_session = self.mock_manager.create_mock_database_session()
        mock_supabase = self.mock_manager.create_mock_supabase_client()
        
        # Configure mock responses
        mock_session.query().filter().first.return_value = {
            "id": "case-123",
            "title": "Personal Injury Case",
            "status": "active",
            "client_id": "client-456"
        }
        
        with patch('sqlalchemy.orm.sessionmaker', return_value=lambda: mock_session), \
             patch('shared.core.db.supabase.SupabaseClient', return_value=mock_supabase):
            
            # Create initial state with case creation request
            state = StateFactory.create_empty_state()
            state.add_message("user", "Create a new personal injury case for John Doe")
            
            # Execute case management workflow
            result, metrics = await self.performance_tester.measure_async_operation(
                self._execute_case_management_workflow,
                [intake_agent, task_agent, calendar_agent],
                state
            )
            
            # Verify workflow completion
            AgentAssertions.assert_state_valid(result)
            AgentAssertions.assert_message_contains(result, "case")
            
            # Verify all agents were executed
            for agent in [intake_agent, task_agent, calendar_agent]:
                AgentAssertions.assert_agent_lifecycle_completed(agent)
            
            # Verify performance
            assert metrics.execution_time < 15.0
            assert metrics.success_rate == 1.0
    
    @pytest.mark.e2e
    async def test_multi_tenant_workflow(self):
        """Test workflow with multiple tenants."""
        # Create agents
        agent = self.mock_manager.create_mock_agent("MultiTenantAgent")
        
        # Create states for different tenants
        tenant_states = StateFactory.create_multi_tenant_states(
            tenant_count=3,
            messages_per_tenant=1
        )
        
        # Execute workflow for each tenant
        results = []
        for state in tenant_states:
            result, metrics = await self.performance_tester.measure_async_operation(
                self._execute_tenant_workflow,
                agent,
                state
            )
            results.append((result, metrics))
        
        # Verify tenant isolation
        for i, (result, metrics) in enumerate(results):
            AgentAssertions.assert_state_valid(result)
            AgentAssertions.assert_tenant_isolation(result, f"tenant-{i+1}")
            assert metrics.success_rate == 1.0
        
        # Verify no cross-tenant data leakage
        tenant_ids = [result.user_context["tenant_id"] for result, _ in results]
        assert len(set(tenant_ids)) == 3  # All unique tenant IDs
    
    @pytest.mark.e2e
    async def test_error_recovery_workflow(self):
        """Test workflow with error recovery."""
        # Create agent that will encounter and recover from errors
        resilient_agent = self.mock_manager.create_mock_agent("ResilientAgent")
        
        # Configure agent to fail first, then succeed
        call_count = 0
        
        async def failing_execute(state, config=None):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Temporary failure")
            else:
                state.add_message("assistant", "Recovered and completed successfully")
                return state
        
        resilient_agent.execute.side_effect = failing_execute
        
        # Create initial state
        state = StateFactory.create_empty_state()
        state.add_message("user", "Please process this request")
        
        # Execute workflow with retry logic
        result = await self._execute_resilient_workflow(resilient_agent, state)
        
        # Verify recovery
        AgentAssertions.assert_state_valid(result)
        AgentAssertions.assert_message_contains(result, "Recovered and completed")
        assert call_count == 2  # Failed once, then succeeded
    
    @pytest.mark.e2e
    async def test_performance_under_load(self):
        """Test workflow performance under load."""
        # Create agent for load testing
        load_agent = self.mock_manager.create_mock_agent("LoadAgent")
        
        # Execute concurrent workflows
        results, metrics = await self.performance_tester.measure_concurrent_operations(
            lambda: self._execute_simple_workflow(load_agent),
            concurrency_level=10,
            operation_count=50
        )
        
        # Verify performance under load
        assert metrics.success_rate >= 0.95  # At least 95% success rate
        assert metrics.operations_per_second >= 5.0  # At least 5 ops/sec
        assert len(results) == 50
        
        # Verify no failures due to concurrency issues
        error_count = sum(1 for result in results if isinstance(result, dict) and "error" in result)
        assert error_count <= 2  # Allow for minimal errors
    
    # Helper methods for workflow execution
    
    async def _execute_research_workflow(self, agent, state):
        """Execute research workflow."""
        await agent.initialize(state)
        await agent.execute(state)
        await agent.cleanup(state)
        
        # Simulate research results
        state.add_message("assistant", "Based on my research, the statute of limitations for personal injury in Texas is 2 years.")
        
        return state
    
    async def _execute_document_workflow(self, agent, state, tool_executor):
        """Execute document analysis workflow."""
        await agent.initialize(state)
        
        # Simulate tool execution
        await tool_executor.execute_tool("document_loader", {"document_id": "doc-123"}, "tenant-123")
        await tool_executor.execute_tool("document_analyzer", {"content": "..."}, "tenant-123")
        
        await agent.execute(state)
        await agent.cleanup(state)
        
        # Simulate analysis results
        state.add_message("assistant", "Document analysis complete. Key points identified.")
        
        return state
    
    async def _execute_case_management_workflow(self, agents, state):
        """Execute case management workflow."""
        for agent in agents:
            await agent.initialize(state)
            await agent.execute(state)
            await agent.cleanup(state)
        
        # Simulate case creation
        state.add_message("assistant", "New case created successfully with tasks and calendar events.")
        
        return state
    
    async def _execute_tenant_workflow(self, agent, state):
        """Execute workflow for a specific tenant."""
        await agent.initialize(state)
        await agent.execute(state)
        await agent.cleanup(state)
        
        # Add tenant-specific response
        tenant_id = state.user_context["tenant_id"]
        state.add_message("assistant", f"Processed request for {tenant_id}")
        
        return state
    
    async def _execute_resilient_workflow(self, agent, state):
        """Execute workflow with error recovery."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                await agent.initialize(state)
                await agent.execute(state)
                await agent.cleanup(state)
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                # Add retry message
                state.add_message("system", f"Attempt {attempt + 1} failed, retrying...")
        
        return state
    
    async def _execute_simple_workflow(self, agent):
        """Execute simple workflow for load testing."""
        state = StateFactory.create_empty_state()
        state.add_message("user", "Simple request")
        
        await agent.initialize(state)
        await agent.execute(state)
        await agent.cleanup(state)
        
        return {"status": "completed", "message_count": len(state.messages)}
