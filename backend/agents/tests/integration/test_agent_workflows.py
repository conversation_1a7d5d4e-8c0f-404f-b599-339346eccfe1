"""
Integration tests for agent workflows.

This module tests the integration between different agent components,
including state management, tool execution, and external service interactions.
"""

import pytest
from unittest.mock import patch

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    AgentFactory,
    AgentAssertions,
    MockManager,
    TestDataManager
)


class TestAgentWorkflows(BaseAgentTest):
    """Test agent workflow integration."""
    
    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()
        self.test_data_manager = TestDataManager("test_data")
    
    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()
    
    @pytest.mark.integration
    async def test_agent_state_persistence_workflow(self):
        """Test agent workflow with state persistence."""
        # Create initial state
        state = StateFactory.create_empty_state()
        state.add_message("user", "I need help with my case")
        
        # Mock state manager
        mock_state_manager = self.mock_manager.create_mock_state_manager()
        
        with patch('shared.core.state.StateManager', return_value=mock_state_manager):
            # Simulate agent processing
            state.add_message("assistant", "I can help you with your case. What specific information do you need?")
            
            # Verify state is valid
            AgentAssertions.assert_state_valid(state)
            AgentAssertions.assert_message_count(state, 2)
            AgentAssertions.assert_message_contains(state, "help with my case")
            AgentAssertions.assert_message_contains(state, "I can help you")
    
    @pytest.mark.integration
    async def test_multi_agent_workflow(self):
        """Test workflow involving multiple agents."""
        # Create agents
        research_agent = self.mock_manager.create_mock_agent("ResearchAgent")
        document_agent = self.mock_manager.create_mock_agent("DocumentAgent")
        
        # Create initial state
        state = StateFactory.create_empty_state()
        state.add_message("user", "I need research on personal injury law and a document summary")
        
        # Execute research agent
        await research_agent.initialize(state)
        await research_agent.execute(state)
        await research_agent.cleanup(state)
        
        # Execute document agent
        await document_agent.initialize(state)
        await document_agent.execute(state)
        await document_agent.cleanup(state)
        
        # Verify both agents were executed
        AgentAssertions.assert_agent_lifecycle_completed(research_agent)
        AgentAssertions.assert_agent_lifecycle_completed(document_agent)
    
    @pytest.mark.integration
    async def test_agent_tool_integration(self):
        """Test agent integration with tool execution."""
        # Create mock tool responses
        tool_responses = {
            "legal_search": {
                "results": [
                    {"title": "Personal Injury Law", "content": "Legal information..."},
                    {"title": "Statute of Limitations", "content": "Time limits..."}
                ]
            },
            "document_analyzer": {
                "summary": "Document analysis complete",
                "key_points": ["Point 1", "Point 2"]
            }
        }
        
        mock_executor = self.mock_manager.create_mock_tool_executor(tool_responses)
        
        # Execute tools
        search_result = await mock_executor.execute_tool("legal_search", {"query": "personal injury"}, "tenant-123")
        analysis_result = await mock_executor.execute_tool("document_analyzer", {"document_id": "doc-123"}, "tenant-123")
        
        # Verify tool execution
        assert search_result["results"][0]["title"] == "Personal Injury Law"
        assert analysis_result["summary"] == "Document analysis complete"
        
        AgentAssertions.assert_tool_executed(mock_executor, "legal_search", 1)
        AgentAssertions.assert_tool_executed(mock_executor, "document_analyzer", 1)
    
    @pytest.mark.integration
    async def test_external_service_integration(self):
        """Test agent integration with external services."""
        # Mock external services
        mock_supabase = self.mock_manager.create_mock_supabase_client()
        mock_pinecone = self.mock_manager.create_mock_pinecone_client()
        mock_openai = self.mock_manager.create_mock_openai_client()
        
        with patch('shared.core.db.supabase.SupabaseClient', return_value=mock_supabase), \
             patch('pinecone.Pinecone', return_value=mock_pinecone), \
             patch('openai.OpenAI', return_value=mock_openai):
            
            # Simulate agent workflow with external services
            state = StateFactory.create_empty_state()
            
            # Mock user lookup
            user_data = mock_supabase.get_user()
            assert user_data["email"] == "<EMAIL>"
            
            # Mock document search
            search_results = mock_pinecone.Index().query()
            assert len(search_results["matches"]) > 0
            
            # Mock LLM completion
            completion = mock_openai.chat.completions.create()
            assert completion.choices[0].message.content is not None
    
    @pytest.mark.integration
    async def test_error_handling_workflow(self):
        """Test error handling in agent workflows."""
        # Create agent that will encounter errors
        mock_agent = self.mock_manager.create_mock_agent("ErrorAgent")
        
        # Configure agent to raise errors
        mock_agent.execute.side_effect = Exception("Simulated error")
        
        state = StateFactory.create_empty_state()
        
        # Execute agent and handle error
        try:
            await mock_agent.initialize(state)
            await mock_agent.execute(state)
        except Exception as e:
            # Add error to state
            state.add_message("system", f"Error: {str(e)}", {"error": True})
        
        # Verify error handling
        AgentAssertions.assert_message_contains(state, "Error: Simulated error")
        
        # Cleanup should still be called
        await mock_agent.cleanup(state)
        mock_agent.cleanup.assert_called_once()
    
    @pytest.mark.integration
    async def test_tenant_isolation_workflow(self):
        """Test tenant isolation in multi-tenant workflows."""
        # Create states for different tenants
        tenant1_state = StateFactory.create_empty_state(tenant_id="tenant-1")
        tenant2_state = StateFactory.create_empty_state(tenant_id="tenant-2")
        
        # Add tenant-specific data
        tenant1_state.add_message("user", "Tenant 1 message")
        tenant2_state.add_message("user", "Tenant 2 message")
        
        # Verify tenant isolation
        AgentAssertions.assert_tenant_isolation(tenant1_state, "tenant-1")
        AgentAssertions.assert_tenant_isolation(tenant2_state, "tenant-2")
        
        # Verify states don't interfere with each other
        assert tenant1_state.user_context["tenant_id"] != tenant2_state.user_context["tenant_id"]
        AgentAssertions.assert_message_contains(tenant1_state, "Tenant 1 message")
        AgentAssertions.assert_message_contains(tenant2_state, "Tenant 2 message")
    
    @pytest.mark.integration
    async def test_configuration_workflow(self):
        """Test agent workflow with different configurations."""
        # Load test configurations
        test_configs = self.test_data_manager.get_test_configurations()
        
        # Create agents with different configurations
        config1 = AgentFactory.create_agent_config(
            name="Agent1",
            model="gpt-4o",
            temperature=0.1
        )
        
        config2 = AgentFactory.create_agent_config(
            name="Agent2",
            model="gpt-3.5-turbo",
            temperature=0.9
        )
        
        agent1 = self.mock_manager.create_mock_agent("Agent1", config1)
        agent2 = self.mock_manager.create_mock_agent("Agent2", config2)
        
        # Verify configurations
        assert agent1.config.temperature == 0.1
        assert agent2.config.temperature == 0.9
        assert agent1.config.model != agent2.config.model
    
    @pytest.mark.integration
    async def test_data_flow_workflow(self):
        """Test data flow between agent components."""
        # Create test data
        test_documents = self.test_data_manager.load_json_data("sample_scenarios")
        
        # Create workflow state
        state = StateFactory.create_empty_state()
        
        # Simulate data processing workflow
        for scenario in test_documents.get("agent_execution", []):
            # Process scenario
            input_state = scenario["input_state"]
            expected_output = scenario["expected_output"]
            
            # Add input messages to state
            for message in input_state["messages"]:
                state.add_message(message["role"], message["content"])
            
            # Verify expected output
            if "message_count" in expected_output:
                AgentAssertions.assert_message_count(state, expected_output["message_count"])
    
    @pytest.mark.integration
    async def test_concurrent_agent_execution(self):
        """Test concurrent execution of multiple agents."""
        import asyncio
        
        # Create multiple agents
        agents = [
            self.mock_manager.create_mock_agent(f"Agent{i}")
            for i in range(3)
        ]
        
        # Create states for each agent
        states = [
            StateFactory.create_empty_state(tenant_id=f"tenant-{i}")
            for i in range(3)
        ]
        
        # Execute agents concurrently
        async def execute_agent(agent, state):
            await agent.initialize(state)
            await agent.execute(state)
            await agent.cleanup(state)
            return state
        
        tasks = [
            execute_agent(agent, state)
            for agent, state in zip(agents, states)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Verify all agents completed
        assert len(results) == 3
        for i, (agent, result_state) in enumerate(zip(agents, results)):
            AgentAssertions.assert_agent_lifecycle_completed(agent)
            AgentAssertions.assert_tenant_isolation(result_state, f"tenant-{i}")
    
    @pytest.mark.integration
    async def test_database_integration_workflow(self):
        """Test agent workflow with database operations."""
        # Mock database session
        mock_session = self.mock_manager.create_mock_database_session()
        
        # Configure mock responses
        mock_session.query().filter().first.return_value = {
            "id": "case-123",
            "title": "Test Case",
            "status": "active"
        }
        
        with patch('sqlalchemy.orm.sessionmaker', return_value=lambda: mock_session):
            # Simulate database operations
            state = StateFactory.create_empty_state()
            
            # Mock case lookup
            case_data = mock_session.query().filter().first()
            assert case_data["title"] == "Test Case"
            
            # Verify database session was used
            mock_session.query.assert_called()
            mock_session.close.assert_called()
