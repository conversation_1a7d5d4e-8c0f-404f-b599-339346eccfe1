"""
Performance tests for agent system.

This module tests the performance characteristics of agents,
including execution time, memory usage, and throughput.
"""

import asyncio
import pytest

from backend.agents.shared.testing import (
    BaseAgentTest,
    StateFactory,
    AgentFactory,
    MockManager,
    PerformanceTester,
    TestDataFactory
)


class TestAgentPerformance(BaseAgentTest):
    """Test agent performance characteristics."""
    
    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.mock_manager = MockManager()
        self.performance_tester = PerformanceTester()
    
    def teardown_method(self):
        """Clean up after each test method."""
        self.mock_manager.cleanup()
    
    @pytest.mark.performance
    async def test_agent_execution_performance(self):
        """Test agent execution performance."""
        agent = self.mock_manager.create_mock_agent("PerformanceAgent")
        state = StateFactory.create_empty_state()
        
        # Measure execution performance
        result, metrics = await self.performance_tester.measure_async_operation(
            self._execute_agent_lifecycle,
            agent,
            state
        )
        
        # Verify performance thresholds
        assert metrics.execution_time < 1.0  # Should complete in under 1 second
        assert metrics.memory_usage_mb < 50.0  # Should use less than 50MB
        assert metrics.success_rate == 1.0
        
        # Validate performance
        self.performance_tester.assert_performance_acceptable(metrics)
    
    @pytest.mark.performance
    async def test_batch_agent_execution_performance(self):
        """Test batch agent execution performance."""
        agent = self.mock_manager.create_mock_agent("BatchAgent")
        
        # Create batch of operations
        batch_size = 20
        operation_args = [
            (StateFactory.create_empty_state(),)
            for _ in range(batch_size)
        ]
        
        # Measure batch performance
        results, metrics = await self.performance_tester.measure_batch_operations(
            lambda state: self._execute_agent_lifecycle(agent, state),
            batch_size=batch_size,
            operation_args=operation_args
        )
        
        # Verify batch performance
        assert len(results) == batch_size
        assert metrics.operations_per_second >= 10.0  # At least 10 ops/sec
        assert metrics.success_rate >= 0.95  # At least 95% success rate
        assert metrics.execution_time < 5.0  # Batch should complete in under 5 seconds
    
    @pytest.mark.performance
    async def test_concurrent_agent_execution_performance(self):
        """Test concurrent agent execution performance."""
        agent = self.mock_manager.create_mock_agent("ConcurrentAgent")
        
        # Measure concurrent performance
        results, metrics = await self.performance_tester.measure_concurrent_operations(
            lambda: self._execute_agent_lifecycle(agent, StateFactory.create_empty_state()),
            concurrency_level=5,
            operation_count=25
        )
        
        # Verify concurrent performance
        assert len(results) == 25
        assert metrics.operations_per_second >= 15.0  # Should be faster with concurrency
        assert metrics.success_rate >= 0.95
        assert metrics.execution_time < 3.0  # Should complete faster than sequential
    
    @pytest.mark.performance
    async def test_memory_usage_performance(self):
        """Test memory usage during agent execution."""
        agent = self.mock_manager.create_mock_agent("MemoryAgent")
        
        # Create large state to test memory handling
        large_state = StateFactory.create_conversation_state(conversation_length=100)
        
        # Measure memory usage
        result, metrics = await self.performance_tester.measure_async_operation(
            self._execute_agent_lifecycle,
            agent,
            large_state
        )
        
        # Verify memory usage is reasonable
        assert metrics.memory_usage_mb < 100.0  # Should not use excessive memory
        assert metrics.peak_memory_mb < 150.0
    
    @pytest.mark.performance
    async def test_state_serialization_performance(self):
        """Test state serialization/deserialization performance."""
        # Create complex state
        state = StateFactory.create_conversation_state(conversation_length=50)
        
        # Add complex metadata
        for i in range(10):
            state.metadata[f"complex_data_{i}"] = {
                "nested": {"data": list(range(100))},
                "text": "x" * 1000
            }
        
        # Measure serialization performance
        result, metrics = await self.performance_tester.measure_async_operation(
            self._serialize_deserialize_state,
            state
        )
        
        # Verify serialization performance
        assert metrics.execution_time < 0.5  # Should serialize quickly
        assert result == state.to_dict()  # Should preserve data integrity
    
    @pytest.mark.performance
    async def test_tool_execution_performance(self):
        """Test tool execution performance."""
        # Create mock tool executor with realistic delays
        mock_executor = self.mock_manager.create_slow_mock(delay=0.1)
        
        # Measure tool execution performance
        results, metrics = await self.performance_tester.measure_batch_operations(
            lambda: mock_executor(),
            batch_size=10
        )
        
        # Verify tool performance
        assert metrics.operations_per_second >= 8.0  # Should handle reasonable throughput
        assert metrics.execution_time < 2.0  # Batch should complete reasonably fast
    
    @pytest.mark.performance
    async def test_large_dataset_performance(self):
        """Test performance with large datasets."""
        # Create large test dataset
        large_dataset = TestDataFactory.create_performance_test_data(
            operation_count=1000,
            data_size="large"
        )
        
        agent = self.mock_manager.create_mock_agent("DatasetAgent")
        
        # Measure performance with large dataset
        result, metrics = await self.performance_tester.measure_async_operation(
            self._process_large_dataset,
            agent,
            large_dataset
        )
        
        # Verify large dataset performance
        assert metrics.execution_time < 10.0  # Should handle large data reasonably
        assert metrics.memory_usage_mb < 200.0  # Should not use excessive memory
    
    @pytest.mark.performance
    async def test_error_handling_performance(self):
        """Test performance impact of error handling."""
        # Create agent that will encounter errors
        error_agent = self.mock_manager.create_mock_agent("ErrorAgent")
        
        # Configure agent to fail 20% of the time
        call_count = 0
        
        async def sometimes_failing_execute(state, config=None):
            nonlocal call_count
            call_count += 1
            if call_count % 5 == 0:  # Fail every 5th call
                raise Exception("Simulated error")
            return state
        
        error_agent.execute.side_effect = sometimes_failing_execute
        
        # Measure performance with errors
        results, metrics = await self.performance_tester.measure_batch_operations(
            lambda: self._execute_agent_with_error_handling(error_agent),
            batch_size=20
        )
        
        # Verify error handling doesn't severely impact performance
        assert metrics.success_rate >= 0.80  # Should handle most operations
        assert metrics.execution_time < 5.0  # Error handling shouldn't be too slow
    
    @pytest.mark.performance
    async def test_scaling_performance(self):
        """Test performance scaling with different loads."""
        agent = self.mock_manager.create_mock_agent("ScalingAgent")
        
        # Test different batch sizes
        batch_sizes = [1, 5, 10, 20, 50]
        scaling_results = []
        
        for batch_size in batch_sizes:
            results, metrics = await self.performance_tester.measure_batch_operations(
                lambda: self._execute_agent_lifecycle(agent, StateFactory.create_empty_state()),
                batch_size=batch_size
            )
            
            scaling_results.append({
                "batch_size": batch_size,
                "ops_per_second": metrics.operations_per_second,
                "execution_time": metrics.execution_time,
                "success_rate": metrics.success_rate
            })
        
        # Verify scaling characteristics
        for result in scaling_results:
            assert result["success_rate"] >= 0.95
            assert result["ops_per_second"] >= 5.0
        
        # Verify throughput scales reasonably
        small_batch_ops = scaling_results[0]["ops_per_second"]
        large_batch_ops = scaling_results[-1]["ops_per_second"]
        
        # Large batches should have higher or similar throughput
        assert large_batch_ops >= small_batch_ops * 0.8
    
    @pytest.mark.performance
    async def test_resource_cleanup_performance(self):
        """Test performance of resource cleanup."""
        agent = self.mock_manager.create_mock_agent("CleanupAgent")
        
        # Create multiple states with resources
        states = [
            StateFactory.create_conversation_state(conversation_length=20)
            for _ in range(10)
        ]
        
        # Measure cleanup performance
        result, metrics = await self.performance_tester.measure_async_operation(
            self._execute_cleanup_workflow,
            agent,
            states
        )
        
        # Verify cleanup performance
        assert metrics.execution_time < 2.0  # Cleanup should be fast
        assert metrics.memory_usage_mb < 50.0  # Should not leak memory
    
    # Helper methods for performance testing
    
    async def _execute_agent_lifecycle(self, agent, state):
        """Execute complete agent lifecycle."""
        await agent.initialize(state)
        await agent.execute(state)
        await agent.cleanup(state)
        return state
    
    async def _serialize_deserialize_state(self, state):
        """Test state serialization/deserialization."""
        # Serialize state
        serialized = state.to_dict()
        
        # Deserialize state
        from shared.core.state import AiLexState
        deserialized = AiLexState.from_dict(serialized)
        
        return serialized
    
    async def _process_large_dataset(self, agent, dataset):
        """Process large dataset with agent."""
        state = StateFactory.create_empty_state()
        
        # Simulate processing dataset
        for operation in dataset["operations"][:100]:  # Process first 100 operations
            state.add_message("system", f"Processed operation {operation['id']}")
        
        await self._execute_agent_lifecycle(agent, state)
        return state
    
    async def _execute_agent_with_error_handling(self, agent):
        """Execute agent with error handling."""
        state = StateFactory.create_empty_state()
        
        try:
            await self._execute_agent_lifecycle(agent, state)
            return {"status": "success"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def _execute_cleanup_workflow(self, agent, states):
        """Execute cleanup workflow for multiple states."""
        for state in states:
            await agent.cleanup(state)
        
        # Simulate resource cleanup
        await asyncio.sleep(0.1)
        
        return {"cleaned_states": len(states)}
