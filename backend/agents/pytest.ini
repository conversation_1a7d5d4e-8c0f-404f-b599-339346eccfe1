[tool:pytest]
# Minimum pytest version required
minversion = 6.0

# Test discovery patterns
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test paths
testpaths = 
    tests
    interactive/*/tests
    matter_client/tests

# Additional options
addopts = 
    -ra
    -q
    --strict-markers
    --strict-config
    --tb=short
    --cov=backend/agents
    --cov-report=term-missing
    --cov-report=html:coverage_html
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --asyncio-mode=auto

# Async test configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:langchain.*
    ignore::UserWarning:langgraph.*

# Custom markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    slow: Slow running tests (may take longer to execute)
    performance: Performance and load tests
    timeout: Tests with specific timeout requirements
    agent: Agent-specific functionality tests
    state: State management and persistence tests
    tools: Tool execution and integration tests
    database: Database-related operations tests
    external_api: Tests that interact with external APIs
    mock_required: Tests that require extensive mocking

# Exclude directories
norecursedirs = 
    .git
    .tox
    .env
    dist
    build
    south_migrations
    __pycache__
    .pytest_cache
    node_modules
    .venv
    venv

# Timeout settings (in seconds)
timeout = 300
timeout_method = thread

# Coverage configuration
[tool:coverage:run]
source = backend/agents
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */conftest.py
    */migrations/*
    */venv/*
    */.venv/*

[tool:coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[tool:coverage:html]
directory = coverage_html

[tool:coverage:xml]
output = coverage.xml
