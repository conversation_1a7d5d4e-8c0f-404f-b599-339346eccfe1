"""
Error Handling Utilities for Agent System

This module provides comprehensive error handling utilities for the agent system,
including error recovery strategies, error categorization, and structured error handling.

Key Features:
- Structured error handling with context preservation
- Error recovery strategies
- Error categorization for monitoring
- Retry mechanisms with exponential backoff
- Error reporting and logging
- Circuit breaker pattern for resilience

Usage:
    from backend.agents.shared.utils.error_handling import (
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        ErrorRecoveryStrategy,
        handle_agent_error
    )
    
    # Create error handler
    error_handler = <PERSON>rror<PERSON><PERSON>ler("my_agent")
    
    # Handle error with recovery
    try:
        # Agent operation
        pass
    except Exception as e:
        recovery_result = error_handler.handle_error(e, context)
"""

from typing import Optional, Dict, Any, Callable, List, Type
from enum import Enum
import logging
import time
import asyncio
from datetime import datetime, timezone
import traceback

# Local imports with fallback
try:
    from ..core.exceptions import (
        BaseAgentError,
        AgentExecutionError,
        AgentTimeoutError,
        AgentResourceError,
        ErrorCategory,
    )
except ImportError:
    # Fallback for standalone testing
    try:
        from core.exceptions import (
            BaseAgentError,
            AgentExecutionError,
            AgentTimeoutError,
            AgentResourceError,
            ErrorCategory,
        )
    except ImportError:
        # Create minimal fallback classes
        class ErrorCategory:
            EXECUTION = "execution"
            TIMEOUT = "timeout"
            RESOURCE = "resource"

        class BaseAgentError(Exception): pass
        class AgentExecutionError(BaseAgentError): pass
        class AgentTimeoutError(BaseAgentError): pass
        class AgentResourceError(BaseAgentError): pass

# Set up logging
logger = logging.getLogger(__name__)


class ErrorRecoveryStrategy(str, Enum):
    """Error recovery strategies."""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAKER = "circuit_breaker"
    FAIL_FAST = "fail_fast"
    GRACEFUL_DEGRADATION = "graceful_degradation"


class RecoveryResult:
    """Result of error recovery attempt."""
    
    def __init__(
        self,
        success: bool,
        strategy_used: ErrorRecoveryStrategy,
        attempts: int,
        final_result: Any = None,
        final_error: Optional[Exception] = None
    ):
        self.success = success
        self.strategy_used = strategy_used
        self.attempts = attempts
        self.final_result = final_result
        self.final_error = final_error


class CircuitBreakerState(str, Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class CircuitBreaker:
    """Circuit breaker for error handling."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = CircuitBreakerState.CLOSED
    
    def call(self, func: Callable, *args, **kwargs):
        """Call function with circuit breaker protection."""
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
            else:
                raise AgentResourceError("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN


class ErrorHandler:
    """Comprehensive error handler for agents."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_counts: Dict[str, int] = {}
        self.recovery_strategies: Dict[Type[Exception], ErrorRecoveryStrategy] = {
            AgentTimeoutError: ErrorRecoveryStrategy.RETRY,
            AgentResourceError: ErrorRecoveryStrategy.CIRCUIT_BREAKER,
            AgentExecutionError: ErrorRecoveryStrategy.FALLBACK,
        }
    
    def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        recovery_strategy: Optional[ErrorRecoveryStrategy] = None
    ) -> RecoveryResult:
        """
        Handle error with appropriate recovery strategy.
        
        Args:
            error: Exception to handle
            context: Error context information
            recovery_strategy: Override recovery strategy
            
        Returns:
            Recovery result
        """
        context = context or {}
        error_type = type(error)
        
        # Determine recovery strategy
        if recovery_strategy is None:
            recovery_strategy = self.recovery_strategies.get(
                error_type,
                ErrorRecoveryStrategy.FAIL_FAST
            )
        
        # Log error
        self._log_error(error, context, recovery_strategy)
        
        # Update error counts
        error_name = error_type.__name__
        self.error_counts[error_name] = self.error_counts.get(error_name, 0) + 1
        
        # Apply recovery strategy
        try:
            if recovery_strategy == ErrorRecoveryStrategy.RETRY:
                return self._retry_strategy(error, context)
            elif recovery_strategy == ErrorRecoveryStrategy.FALLBACK:
                return self._fallback_strategy(error, context)
            elif recovery_strategy == ErrorRecoveryStrategy.CIRCUIT_BREAKER:
                return self._circuit_breaker_strategy(error, context)
            elif recovery_strategy == ErrorRecoveryStrategy.GRACEFUL_DEGRADATION:
                return self._graceful_degradation_strategy(error, context)
            else:  # FAIL_FAST
                return self._fail_fast_strategy(error, context)
                
        except Exception as recovery_error:
            logger.error(f"Error recovery failed: {recovery_error}")
            return RecoveryResult(
                success=False,
                strategy_used=recovery_strategy,
                attempts=1,
                final_error=recovery_error
            )
    
    def _retry_strategy(
        self,
        error: Exception,
        context: Dict[str, Any],
        max_attempts: int = 3,
        base_delay: float = 1.0
    ) -> RecoveryResult:
        """Retry strategy with exponential backoff."""
        operation = context.get("operation")
        if not operation:
            return RecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.RETRY,
                attempts=0,
                final_error=AgentExecutionError("No operation provided for retry")
            )
        
        for attempt in range(max_attempts):
            if attempt > 0:
                delay = base_delay * (2 ** (attempt - 1))
                logger.info(f"Retrying operation after {delay}s delay (attempt {attempt + 1}/{max_attempts})")
                time.sleep(delay)
            
            try:
                result = operation()
                logger.info(f"Operation succeeded on attempt {attempt + 1}")
                return RecoveryResult(
                    success=True,
                    strategy_used=ErrorRecoveryStrategy.RETRY,
                    attempts=attempt + 1,
                    final_result=result
                )
            except Exception as retry_error:
                logger.warning(f"Retry attempt {attempt + 1} failed: {retry_error}")
                if attempt == max_attempts - 1:
                    return RecoveryResult(
                        success=False,
                        strategy_used=ErrorRecoveryStrategy.RETRY,
                        attempts=max_attempts,
                        final_error=retry_error
                    )
        
        return RecoveryResult(
            success=False,
            strategy_used=ErrorRecoveryStrategy.RETRY,
            attempts=max_attempts,
            final_error=error
        )
    
    def _fallback_strategy(self, error: Exception, context: Dict[str, Any]) -> RecoveryResult:
        """Fallback strategy using alternative operation."""
        fallback_operation = context.get("fallback_operation")
        if not fallback_operation:
            return RecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.FALLBACK,
                attempts=1,
                final_error=AgentExecutionError("No fallback operation provided")
            )
        
        try:
            result = fallback_operation()
            logger.info("Fallback operation succeeded")
            return RecoveryResult(
                success=True,
                strategy_used=ErrorRecoveryStrategy.FALLBACK,
                attempts=1,
                final_result=result
            )
        except Exception as fallback_error:
            logger.error(f"Fallback operation failed: {fallback_error}")
            return RecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.FALLBACK,
                attempts=1,
                final_error=fallback_error
            )
    
    def _circuit_breaker_strategy(self, error: Exception, context: Dict[str, Any]) -> RecoveryResult:
        """Circuit breaker strategy for resource protection."""
        operation_name = context.get("operation_name", "default")
        
        if operation_name not in self.circuit_breakers:
            self.circuit_breakers[operation_name] = CircuitBreaker()
        
        circuit_breaker = self.circuit_breakers[operation_name]
        
        try:
            operation = context.get("operation")
            if not operation:
                raise AgentExecutionError("No operation provided for circuit breaker")
            
            result = circuit_breaker.call(operation)
            return RecoveryResult(
                success=True,
                strategy_used=ErrorRecoveryStrategy.CIRCUIT_BREAKER,
                attempts=1,
                final_result=result
            )
        except Exception as cb_error:
            return RecoveryResult(
                success=False,
                strategy_used=ErrorRecoveryStrategy.CIRCUIT_BREAKER,
                attempts=1,
                final_error=cb_error
            )
    
    def _graceful_degradation_strategy(self, error: Exception, context: Dict[str, Any]) -> RecoveryResult:
        """Graceful degradation strategy."""
        degraded_result = context.get("degraded_result", {"status": "degraded", "error": str(error)})
        
        logger.warning(f"Applying graceful degradation due to error: {error}")
        return RecoveryResult(
            success=True,
            strategy_used=ErrorRecoveryStrategy.GRACEFUL_DEGRADATION,
            attempts=1,
            final_result=degraded_result
        )
    
    def _fail_fast_strategy(self, error: Exception, context: Dict[str, Any]) -> RecoveryResult:
        """Fail fast strategy - no recovery attempted."""
        logger.error(f"Failing fast due to error: {error}")
        return RecoveryResult(
            success=False,
            strategy_used=ErrorRecoveryStrategy.FAIL_FAST,
            attempts=1,
            final_error=error
        )
    
    def _log_error(
        self,
        error: Exception,
        context: Dict[str, Any],
        recovery_strategy: ErrorRecoveryStrategy
    ):
        """Log error with structured information."""
        error_info = {
            "agent_name": self.agent_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "recovery_strategy": recovery_strategy.value,
            "context": context,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "stack_trace": traceback.format_exc(),
        }
        
        if isinstance(error, BaseAgentError):
            error_info.update(error.to_dict())
        
        logger.error(f"Agent error occurred: {error}", extra=error_info)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        return {
            "agent_name": self.agent_name,
            "error_counts": self.error_counts.copy(),
            "circuit_breaker_states": {
                name: cb.state.value
                for name, cb in self.circuit_breakers.items()
            },
            "total_errors": sum(self.error_counts.values()),
        }


def handle_agent_error(
    error: Exception,
    agent_name: str,
    context: Optional[Dict[str, Any]] = None
) -> RecoveryResult:
    """
    Convenience function to handle agent errors.
    
    Args:
        error: Exception to handle
        agent_name: Name of the agent
        context: Error context
        
    Returns:
        Recovery result
    """
    error_handler = ErrorHandler(agent_name)
    return error_handler.handle_error(error, context)


__all__ = [
    "ErrorRecoveryStrategy",
    "RecoveryResult",
    "CircuitBreaker",
    "CircuitBreakerState",
    "ErrorHandler",
    "handle_agent_error",
]
