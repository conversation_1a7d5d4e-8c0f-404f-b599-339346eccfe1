"""
Shared Agent Utilities

This package provides common utility functions for all agents in the system,
including validation, error handling, logging, and monitoring capabilities.

Key Components:
- Validation: Input validation and tenant access control
- Error Handling: Structured error handling and recovery
- Logging: Structured logging for agents
- Monitoring: Performance metrics and health monitoring

Usage:
    from backend.agents.shared.utils.validation import validate_tenant_access
    from backend.agents.shared.utils.logging import StructuredLogger
    from backend.agents.shared.utils.monitoring import AgentMetrics
    from backend.agents.shared.utils.error_handling import ErrorHandler
    
Example:
    # Validation
    if not validate_tenant_access(tenant_id, user_id):
        raise AgentAuthorizationError("Access denied")
    
    # Logging
    logger = StructuredLogger("my_agent")
    logger.info("Agent started", extra={"tenant_id": tenant_id})
    
    # Monitoring
    metrics = AgentMetrics("my_agent", "research")
    metrics.record_execution_start()
"""

# Import with error handling for standalone testing
try:
    from .validation import (
        validate_tenant_access,
        validate_agent_config,
        validate_input_data,
        AgentValidationError,
    )
    from .error_handling import (
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>rRecoveryStrategy,
        handle_agent_error,
    )
    from .logging import (
        StructuredLogger,
        get_logger,
        log_security_event,
        log_performance_event,
    )
    from .monitoring import (
        AgentMetrics,
        PerformanceTracker,
        HealthChecker,
    )
except ImportError as e:
    # Provide minimal fallbacks for testing
    def validate_tenant_access(tenant_id, user_id): return True
    def validate_agent_config(config): pass
    def validate_input_data(data, **kwargs): pass

    class AgentValidationError(Exception): pass
    class ErrorHandler:
        def __init__(self, name): pass
    class ErrorRecoveryStrategy: pass
    class StructuredLogger:
        def __init__(self, name): pass
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
    class AgentMetrics:
        def __init__(self, name, type): pass
    class PerformanceTracker:
        def __init__(self, name): pass
    class HealthChecker:
        def __init__(self, name): pass

    def get_logger(name): return StructuredLogger(name)
    def log_security_event(event, context): pass
    def log_performance_event(metric, value, context): pass
    def handle_agent_error(error, name, context): pass

__all__ = [
    # Validation
    "validate_tenant_access",
    "validate_agent_config", 
    "validate_input_data",
    "AgentValidationError",
    
    # Error handling
    "ErrorHandler",
    "ErrorRecoveryStrategy",
    "handle_agent_error",
    
    # Logging
    "StructuredLogger",
    "get_logger",
    "log_security_event",
    "log_performance_event",
    
    # Monitoring
    "AgentMetrics",
    "PerformanceTracker",
    "HealthChecker",
]
