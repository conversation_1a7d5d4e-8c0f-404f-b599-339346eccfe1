"""
Validation Utilities for Agent System

This module provides comprehensive validation utilities for the agent system,
including tenant access control, input validation, and configuration validation.

Key Features:
- Tenant isolation validation
- User authorization checks
- Input data validation
- Configuration validation
- Security validation
- Performance validation

Usage:
    from backend.agents.shared.utils.validation import (
        validate_tenant_access,
        validate_agent_config,
        validate_input_data
    )
    
    # Validate tenant access
    if not validate_tenant_access(tenant_id, user_id):
        raise AgentAuthorizationError("Access denied")
    
    # Validate configuration
    validate_agent_config(config)
    
    # Validate input data
    validate_input_data(input_data, schema)
"""

from typing import Any, Dict, Optional, List, Union
from pydantic import ValidationError
import re
import logging

# Set up logging
logger = logging.getLogger(__name__)


class AgentValidationError(Exception):
    """Custom validation error for agents."""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(message)
        self.field = field
        self.value = value


def validate_tenant_access(tenant_id: str, user_id: str) -> bool:
    """
    Validate user has access to tenant.
    
    This function checks if a user has permission to access resources
    within a specific tenant. It implements tenant isolation security.
    
    Args:
        tenant_id: Tenant ID to validate access for
        user_id: User ID requesting access
        
    Returns:
        True if access is allowed, False otherwise
    """
    try:
        # Basic validation
        if not tenant_id or not user_id:
            logger.warning(f"Invalid tenant_id or user_id: {tenant_id}, {user_id}")
            return False
        
        # Validate format (basic UUID format check)
        if not _is_valid_uuid_format(tenant_id) or not _is_valid_uuid_format(user_id):
            logger.warning(f"Invalid UUID format: tenant_id={tenant_id}, user_id={user_id}")
            return False
        
        # TODO: Implement actual tenant access validation
        # This would typically involve:
        # 1. Database lookup to check user-tenant relationship
        # 2. Role-based access control (RBAC) validation
        # 3. Permission checks
        # 4. Active subscription validation
        
        # For now, return True for valid format
        # In production, this should be replaced with actual validation logic
        logger.debug(f"Tenant access validated: tenant_id={tenant_id}, user_id={user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating tenant access: {e}")
        return False


def validate_agent_config(config: Dict[str, Any]) -> None:
    """
    Validate agent configuration.
    
    Args:
        config: Agent configuration dictionary
        
    Raises:
        AgentValidationError: If configuration is invalid
    """
    if not isinstance(config, dict):
        raise AgentValidationError("Configuration must be a dictionary")
    
    # Validate required fields
    required_fields = []  # Add required fields as needed
    for field in required_fields:
        if field not in config:
            raise AgentValidationError(f"Required field '{field}' missing from configuration", field=field)
    
    # Validate specific configuration values
    if "max_execution_time" in config:
        max_time = config["max_execution_time"]
        if not isinstance(max_time, (int, float)) or max_time <= 0:
            raise AgentValidationError(
                "max_execution_time must be a positive number",
                field="max_execution_time",
                value=max_time
            )
    
    if "max_iterations" in config:
        max_iter = config["max_iterations"]
        if not isinstance(max_iter, int) or max_iter <= 0:
            raise AgentValidationError(
                "max_iterations must be a positive integer",
                field="max_iterations", 
                value=max_iter
            )
    
    logger.debug("Agent configuration validated successfully")


def validate_input_data(
    data: Dict[str, Any],
    schema: Optional[Dict[str, Any]] = None,
    required_fields: Optional[List[str]] = None
) -> None:
    """
    Validate input data against schema and requirements.
    
    Args:
        data: Input data to validate
        schema: Optional schema to validate against
        required_fields: Optional list of required fields
        
    Raises:
        AgentValidationError: If validation fails
    """
    if not isinstance(data, dict):
        raise AgentValidationError("Input data must be a dictionary")
    
    # Check required fields
    if required_fields:
        for field in required_fields:
            if field not in data:
                raise AgentValidationError(f"Required field '{field}' missing", field=field)
            
            # Check for empty values
            value = data[field]
            if value is None or (isinstance(value, str) and not value.strip()):
                raise AgentValidationError(f"Required field '{field}' cannot be empty", field=field, value=value)
    
    # Validate against schema if provided
    if schema:
        _validate_against_schema(data, schema)
    
    logger.debug("Input data validated successfully")


def validate_user_permissions(
    user_id: str,
    tenant_id: str,
    required_permissions: List[str]
) -> bool:
    """
    Validate user has required permissions.
    
    Args:
        user_id: User ID to check permissions for
        tenant_id: Tenant ID for context
        required_permissions: List of required permissions
        
    Returns:
        True if user has all required permissions, False otherwise
    """
    try:
        # TODO: Implement actual permission checking
        # This would typically involve:
        # 1. Fetching user roles and permissions from database
        # 2. Checking against required permissions
        # 3. Considering tenant-specific permissions
        
        # For now, return True for basic validation
        logger.debug(f"User permissions validated: user_id={user_id}, permissions={required_permissions}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating user permissions: {e}")
        return False


def validate_message_content(content: str, max_length: int = 10000) -> None:
    """
    Validate message content for safety and length.
    
    Args:
        content: Message content to validate
        max_length: Maximum allowed length
        
    Raises:
        AgentValidationError: If content is invalid
    """
    if not isinstance(content, str):
        raise AgentValidationError("Message content must be a string")
    
    if len(content) > max_length:
        raise AgentValidationError(
            f"Message content exceeds maximum length of {max_length} characters",
            field="content",
            value=len(content)
        )
    
    # Check for potentially harmful content
    if _contains_harmful_content(content):
        raise AgentValidationError("Message content contains potentially harmful elements")
    
    logger.debug("Message content validated successfully")


def _is_valid_uuid_format(value: str) -> bool:
    """Check if value matches UUID format."""
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    return bool(uuid_pattern.match(value))


def _validate_against_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> None:
    """Validate data against a schema."""
    # Basic schema validation implementation
    # In production, this could use jsonschema or pydantic
    for field, field_schema in schema.items():
        if field in data:
            value = data[field]
            field_type = field_schema.get("type")
            
            if field_type and not isinstance(value, field_type):
                raise AgentValidationError(
                    f"Field '{field}' must be of type {field_type.__name__}",
                    field=field,
                    value=value
                )


def _contains_harmful_content(content: str) -> bool:
    """Check for potentially harmful content."""
    # Basic implementation - in production, this would be more sophisticated
    harmful_patterns = [
        r'<script[^>]*>',  # Script tags
        r'javascript:',     # JavaScript URLs
        r'data:text/html',  # Data URLs
    ]
    
    for pattern in harmful_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            return True
    
    return False


__all__ = [
    "AgentValidationError",
    "validate_tenant_access",
    "validate_agent_config",
    "validate_input_data",
    "validate_user_permissions",
    "validate_message_content",
]
