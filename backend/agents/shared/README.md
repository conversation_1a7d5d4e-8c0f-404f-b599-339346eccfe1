# Shared Agent Infrastructure

This directory contains the shared infrastructure components for the agent system, providing a comprehensive foundation for consistent agent implementation patterns across the entire system.

## 🏗️ Architecture Overview

The shared infrastructure follows a layered architecture:

```
backend/agents/shared/
├── core/                    # Core infrastructure components
│   ├── base_agent.py       # Enhanced BaseAgent class
│   ├── state.py            # State management with validation
│   └── exceptions.py       # Standardized exception handling
├── utils/                  # Common utilities
│   ├── validation.py       # Input validation and tenant access
│   ├── error_handling.py   # Error recovery and circuit breakers
│   ├── logging.py          # Structured logging
│   └── monitoring.py       # Performance metrics and health
├── testing/                # Testing infrastructure
│   ├── base_test.py        # Base test classes
│   ├── fixtures.py         # Test data and fixtures
│   └── mocks.py            # Mock objects for testing
└── tests/                  # Tests for shared components
```

## 🚀 Quick Start

### Creating a New Agent

```python
from backend.agents.shared.core.base_agent import BaseAgent
from backend.agents.shared.core.state import AgentStatus

class MyAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            agent_type="my_agent",
            agent_name="MyCustomAgent"
        )
    
    async def initialize(self, state, config):
        """Initialize agent-specific state."""
        state["status"] = AgentStatus.INITIALIZING
        # Add initialization logic here
        return state
    
    async def execute(self, state, config):
        """Execute agent-specific logic."""
        state["status"] = AgentStatus.EXECUTING
        
        # Your agent logic here
        result = await self.process_request(state)
        state["result"] = result
        
        state["status"] = AgentStatus.COMPLETED
        return state
    
    async def cleanup(self, state, config):
        """Cleanup agent-specific resources."""
        # Cleanup logic here
        return state
    
    async def process_request(self, state):
        """Custom processing logic."""
        return {"processed": True}
```

### Using the Agent

```python
# Create agent instance
agent = MyAgent()

# Prepare input data
input_data = {
    "messages": [{"role": "user", "content": "Hello"}],
    "request_data": {"key": "value"}
}

# Configure execution
config = {
    "configurable": {
        "tenant_id": "tenant-123",
        "user_id": "user-456",
        "thread_id": "thread-789"
    }
}

# Execute agent
result = await agent.invoke(input_data, config)

# Check result
if result["status"] == AgentStatus.COMPLETED:
    print("Agent executed successfully!")
    print(f"Result: {result.get('result')}")
```

## 🧩 Core Components

### BaseAgent Class

The enhanced `BaseAgent` provides:

- **Lifecycle Management**: Initialize → Execute → Cleanup
- **State Validation**: Comprehensive state validation with tenant isolation
- **Error Handling**: Structured error handling with recovery strategies
- **Performance Tracking**: Built-in metrics and performance monitoring
- **Security**: Tenant isolation and authorization checks
- **Monitoring**: Health checks and operational metrics

Key features:
- Async/await support throughout
- Middleware system for cross-cutting concerns
- Comprehensive error handling and recovery
- Performance metrics and monitoring
- Tenant isolation and security

### State Management

Enhanced state management with:

```python
from backend.agents.shared.core.state import (
    BaseLangGraphState,
    AgentStatus,
    AgentExecutionContext,
    create_execution_context
)

# Create execution context
context = create_execution_context(
    tenant_id="tenant-123",
    user_id="user-456",
    agent_type="research"
)

# State includes comprehensive tracking
state = {
    "messages": [],
    "execution_context": context,
    "status": AgentStatus.PENDING,
    "memory": {},
    "context": {},
    "errors": [],
    "tool_calls": [],
    "metadata": {}
}
```

### Exception Handling

Structured exception hierarchy:

```python
from backend.agents.shared.core.exceptions import (
    AgentExecutionError,
    AgentValidationError,
    AgentAuthorizationError,
    AgentTimeoutError
)

try:
    result = await agent.execute(state, config)
except AgentValidationError as e:
    # Handle validation errors
    logger.error(f"Validation failed: {e}")
except AgentAuthorizationError as e:
    # Handle authorization errors
    logger.error(f"Authorization failed: {e}")
except AgentTimeoutError as e:
    # Handle timeout errors
    logger.error(f"Execution timed out: {e}")
```

## 🛠️ Utilities

### Validation

```python
from backend.agents.shared.utils.validation import (
    validate_tenant_access,
    validate_agent_config,
    validate_input_data
)

# Validate tenant access
if not validate_tenant_access(tenant_id, user_id):
    raise AgentAuthorizationError("Access denied")

# Validate configuration
validate_agent_config(config)

# Validate input data
validate_input_data(input_data, required_fields=["messages"])
```

### Logging

```python
from backend.agents.shared.utils.logging import (
    StructuredLogger,
    log_security_event,
    log_performance_event
)

# Create structured logger
logger = StructuredLogger("my_agent")
logger.info("Agent started", extra={"tenant_id": tenant_id})

# Log security events
log_security_event("unauthorized_access", {"user_id": user_id})

# Log performance events
log_performance_event("execution_time", 1.5, {"agent_type": "research"})
```

### Monitoring

```python
from backend.agents.shared.utils.monitoring import (
    AgentMetrics,
    PerformanceTracker,
    HealthChecker
)

# Create metrics collector
metrics = AgentMetrics("my_agent", "research")
metrics.record_execution_start()
# ... agent execution ...
metrics.record_execution_success(execution_time)

# Track performance
tracker = PerformanceTracker("my_agent")
with tracker.time_operation("search"):
    # Perform operation
    results = await search_operation()

# Check health
health_checker = HealthChecker("my_agent")
health_checker.register_health_check("database", check_database_connection)
status = health_checker.get_health_status()
```

### Error Handling

```python
from backend.agents.shared.utils.error_handling import (
    ErrorHandler,
    ErrorRecoveryStrategy
)

# Create error handler
error_handler = ErrorHandler("my_agent")

# Handle errors with recovery
try:
    result = await risky_operation()
except Exception as e:
    recovery_result = error_handler.handle_error(
        e,
        context={"operation": retry_operation},
        recovery_strategy=ErrorRecoveryStrategy.RETRY
    )
    
    if recovery_result.success:
        result = recovery_result.final_result
    else:
        raise recovery_result.final_error
```

## 🧪 Testing

### Base Test Class

```python
from backend.agents.shared.testing.base_test import BaseAgentTest

class TestMyAgent(BaseAgentTest):
    def setUp(self):
        super().setUp()
        self.agent = MyAgent()
    
    def test_agent_execution(self):
        """Test basic agent execution."""
        result = self.run_agent_test_sync(self.agent)
        self.assert_agent_success(result)
    
    def test_error_handling(self):
        """Test error handling."""
        # Test implementation
        pass
    
    def test_performance(self):
        """Test performance characteristics."""
        metrics = self.create_performance_test(self.agent, iterations=10)
        self.assert_performance_acceptable(metrics["avg_time"])
```

### Test Fixtures

```python
from backend.agents.shared.testing.fixtures import (
    create_test_context,
    create_test_state,
    sample_messages
)

# Create test data
context = create_test_context(tenant_id="test-tenant")
state = create_test_state(context, conversation_type="research")
messages = sample_messages("intake")
```

### Mock Objects

```python
from backend.agents.shared.testing.mocks import (
    MockLLM,
    MockDatabase,
    MockTool
)

# Create mock LLM
mock_llm = MockLLM()
mock_llm.set_response("Mock response")

# Create mock tool
mock_tool = MockTool("search", {"results": ["result1", "result2"]})

# Create mock database
mock_db = MockDatabase()
mock_db.set_data("cases", [{"id": 1, "title": "Test Case"}])
```

## 📊 Performance and Monitoring

### Metrics Collection

The shared infrastructure automatically collects:

- **Execution Metrics**: Success rate, execution time, iteration count
- **Error Metrics**: Error rates by type, recovery success rates
- **Resource Metrics**: Memory usage, CPU time, I/O operations
- **Business Metrics**: Tenant usage, feature utilization

### Health Monitoring

Built-in health checks for:

- **Agent Health**: Execution status, error rates, performance
- **Resource Health**: Memory usage, connection pools, external services
- **Security Health**: Authentication status, authorization checks
- **Configuration Health**: Configuration validation, feature flags

### Performance Optimization

- **Caching**: Built-in caching for frequently accessed data
- **Connection Pooling**: Efficient resource management
- **Circuit Breakers**: Automatic failure handling and recovery
- **Rate Limiting**: Protection against overload

## 🔒 Security Features

### Tenant Isolation

- **Data Isolation**: Strict tenant data separation
- **Access Control**: Role-based access control (RBAC)
- **Audit Logging**: Comprehensive audit trails
- **Encryption**: Data encryption at rest and in transit

### Authorization

- **User Validation**: Multi-level user validation
- **Permission Checks**: Granular permission validation
- **Session Management**: Secure session handling
- **Token Validation**: JWT token validation and refresh

## 📈 Migration Guide

### From Existing Agents

1. **Update Imports**: Change imports to use shared infrastructure
2. **Extend BaseAgent**: Inherit from the enhanced BaseAgent
3. **Update State Management**: Use new state management patterns
4. **Add Error Handling**: Implement structured error handling
5. **Update Tests**: Use new testing infrastructure

### Example Migration

```python
# Before
class OldAgent:
    def __init__(self):
        self.name = "old_agent"
    
    def execute(self, input_data):
        # Old implementation
        return {"result": "success"}

# After
from backend.agents.shared.core.base_agent import BaseAgent

class NewAgent(BaseAgent):
    def __init__(self):
        super().__init__(agent_type="new_agent")
    
    async def initialize(self, state, config):
        return state
    
    async def execute(self, state, config):
        # New implementation with enhanced features
        state["result"] = "success"
        return state
    
    async def cleanup(self, state, config):
        return state
```

## 🤝 Contributing

When contributing to the shared infrastructure:

1. **Follow Patterns**: Use established patterns and conventions
2. **Add Tests**: Include comprehensive tests for new features
3. **Update Documentation**: Keep documentation current
4. **Performance**: Consider performance implications
5. **Security**: Follow security best practices
6. **Backward Compatibility**: Maintain backward compatibility when possible

## 📚 API Reference

For detailed API documentation, see the individual module documentation:

- [BaseAgent API](./core/base_agent.py)
- [State Management API](./core/state.py)
- [Exception Handling API](./core/exceptions.py)
- [Validation API](./utils/validation.py)
- [Logging API](./utils/logging.py)
- [Monitoring API](./utils/monitoring.py)
- [Testing API](./testing/base_test.py)
