#!/usr/bin/env python3
"""
Basic test script to validate shared agent infrastructure.

This script tests the core functionality without external dependencies.
"""

import asyncio
import sys
import traceback
from typing import Dict, Any

# Add the current directory to Python path
sys.path.insert(0, '.')

try:
    from core.base_agent import BaseAgent
    from core.state import <PERSON><PERSON><PERSON><PERSON>, AgentExecutionContext, create_execution_context
    from core.exceptions import AgentExecutionError, AgentValidationError
    print("✓ Successfully imported core modules")
except ImportError as e:
    print(f"✗ Failed to import core modules: {e}")
    traceback.print_exc()
    sys.exit(1)


class TestAgent(BaseAgent):
    """Simple test agent for validation."""
    
    def __init__(self):
        super().__init__(agent_type="test", agent_name="TestAgent")
        print("✓ TestAgent initialized successfully")
    
    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        print("✓ TestAgent.initialize() called")
        state["initialized"] = True
        return state
    
    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        print("✓ TestAgent.execute() called")
        state["executed"] = True
        state["result"] = "Test execution completed"
        return state
    
    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        print("✓ TestAgent.cleanup() called")
        state["cleaned_up"] = True
        return state


async def test_execution_context():
    """Test execution context creation."""
    print("\n--- Testing Execution Context ---")
    
    try:
        context = create_execution_context(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="test"
        )
        
        assert context.tenant_id == "test-tenant-123"
        assert context.user_id == "test-user-456"
        assert context.agent_type == "test"
        assert context.execution_id is not None
        
        print("✓ Execution context created and validated successfully")
        return True
        
    except Exception as e:
        print(f"✗ Execution context test failed: {e}")
        traceback.print_exc()
        return False


async def test_agent_lifecycle():
    """Test agent lifecycle methods."""
    print("\n--- Testing Agent Lifecycle ---")
    
    try:
        agent = TestAgent()
        
        # Create test context and state
        context = create_execution_context(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="test"
        )
        
        state = agent.create_initial_state(context)
        config = {}
        
        # Test lifecycle methods
        state = await agent.initialize(state, config)
        assert state.get("initialized") == True
        
        state = await agent.execute(state, config)
        assert state.get("executed") == True
        assert state.get("result") == "Test execution completed"
        
        state = await agent.cleanup(state, config)
        assert state.get("cleaned_up") == True
        
        print("✓ Agent lifecycle test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Agent lifecycle test failed: {e}")
        traceback.print_exc()
        return False


async def test_agent_invoke():
    """Test agent invoke method."""
    print("\n--- Testing Agent Invoke ---")
    
    try:
        agent = TestAgent()
        
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": "test-tenant-123",
                "user_id": "test-user-456",
            }
        }
        
        result = await agent.invoke(input_data, config)
        
        assert result.get("status") == AgentStatus.COMPLETED
        assert result.get("initialized") == True
        assert result.get("executed") == True
        assert result.get("cleaned_up") == True
        assert result.get("result") == "Test execution completed"
        
        print("✓ Agent invoke test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Agent invoke test failed: {e}")
        traceback.print_exc()
        return False


async def test_error_handling():
    """Test error handling."""
    print("\n--- Testing Error Handling ---")
    
    class FailingAgent(BaseAgent):
        def __init__(self):
            super().__init__(agent_type="failing_test")
        
        async def initialize(self, state, config):
            return state
        
        async def execute(self, state, config):
            raise AgentExecutionError("Test error")
        
        async def cleanup(self, state, config):
            return state
    
    try:
        agent = FailingAgent()
        
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": "test-tenant-123",
                "user_id": "test-user-456",
            }
        }
        
        try:
            await agent.invoke(input_data, config)
            print("✗ Expected AgentExecutionError but none was raised")
            return False
        except AgentExecutionError:
            print("✓ AgentExecutionError properly raised and caught")
            return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        traceback.print_exc()
        return False


async def test_validation():
    """Test validation errors."""
    print("\n--- Testing Validation ---")
    
    try:
        agent = TestAgent()
        
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                # Missing tenant_id and user_id
            }
        }
        
        try:
            await agent.invoke(input_data, config)
            print("✗ Expected AgentValidationError but none was raised")
            return False
        except AgentValidationError:
            print("✓ AgentValidationError properly raised and caught")
            return True
        
    except Exception as e:
        print(f"✗ Validation test failed: {e}")
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🧪 Starting Basic Agent Infrastructure Tests")
    print("=" * 50)
    
    tests = [
        test_execution_context,
        test_agent_lifecycle,
        test_agent_invoke,
        test_error_handling,
        test_validation,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Shared infrastructure is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
