"""
Tests for BaseAgent Class

This module tests the enhanced BaseAgent class functionality,
including lifecycle management, error handling, and performance tracking.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock
from typing import Dict, Any

from ..core.base_agent import BaseAgent
from ..core.state import AgentStatus, AgentExecutionContext
from ..core.exceptions import (
    AgentExecutionError,
    AgentValidationError,
    AgentAuthorizationError,
    AgentTimeoutError,
)
from ..testing.base_test import BaseAgentTest
from ..testing.fixtures import create_test_context, create_test_state
from ..testing.mocks import MockAgent


class TestAgent(BaseAgent):
    """Test agent implementation for testing."""
    
    def __init__(self, **kwargs):
        super().__init__(agent_type="test", **kwargs)
        self.initialize_called = False
        self.execute_called = False
        self.cleanup_called = False
    
    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.initialize_called = True
        state["initialized"] = True
        return state
    
    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.execute_called = True
        state["executed"] = True
        return state
    
    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        self.cleanup_called = True
        state["cleaned_up"] = True
        return state


class TestFailingAgent(BaseAgent):
    """Test agent that fails during execution."""
    
    def __init__(self, **kwargs):
        super().__init__(agent_type="test_failing", **kwargs)
    
    async def initialize(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        return state
    
    async def execute(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        raise AgentExecutionError("Test execution failure")
    
    async def cleanup(self, state: Dict[str, Any], config) -> Dict[str, Any]:
        return state


class TestBaseAgent(BaseAgentTest):
    """Test cases for BaseAgent class."""
    
    def setUp(self):
        super().setUp()
        self.agent = TestAgent()
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.agent_type, "test")
        self.assertEqual(self.agent.agent_name, "TestAgent")
        self.assertIsNotNone(self.agent.logger)
        self.assertIsNotNone(self.agent.metrics)
        self.assertIsNotNone(self.agent.error_handler)
    
    def test_create_execution_context(self):
        """Test execution context creation."""
        context = self.agent.create_execution_context(
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id
        )
        
        self.assertIsInstance(context, AgentExecutionContext)
        self.assertEqual(context.tenant_id, self.test_tenant_id)
        self.assertEqual(context.user_id, self.test_user_id)
        self.assertEqual(context.agent_type, "test")
        self.assertEqual(context.agent_name, "TestAgent")
    
    def test_create_initial_state(self):
        """Test initial state creation."""
        context = self.agent.create_execution_context(
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id
        )
        
        state = self.agent.create_initial_state(context)
        
        self.assert_state_valid(state)
        self.assertEqual(state["status"], AgentStatus.PENDING)
        self.assertEqual(state["execution_context"], context)
    
    async def test_agent_lifecycle(self):
        """Test complete agent lifecycle."""
        await super().test_agent_lifecycle(self.agent)
        
        # Verify lifecycle methods were called
        self.assertTrue(self.agent.initialize_called)
        self.assertTrue(self.agent.execute_called)
        self.assertTrue(self.agent.cleanup_called)
    
    def test_agent_lifecycle_sync(self):
        """Test agent lifecycle synchronously."""
        asyncio.run(self.test_agent_lifecycle())
    
    async def test_agent_invoke(self):
        """Test agent invoke method."""
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }
        
        result = await self.agent.invoke(input_data, config)
        
        self.assert_agent_success(result)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))
    
    def test_agent_invoke_sync(self):
        """Test agent invoke method synchronously."""
        asyncio.run(self.test_agent_invoke())
    
    async def test_agent_call(self):
        """Test agent __call__ method."""
        state = create_test_state()
        config = {"configurable": {"tenant_id": self.test_tenant_id, "user_id": self.test_user_id}}
        
        result = await self.agent(state, config)
        
        self.assert_state_valid(result)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))
    
    def test_agent_call_sync(self):
        """Test agent __call__ method synchronously."""
        asyncio.run(self.test_agent_call())
    
    async def test_agent_validation_error(self):
        """Test agent validation error handling."""
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                # Missing tenant_id and user_id
            }
        }
        
        with self.assertRaises(AgentValidationError):
            await self.agent.invoke(input_data, config)
    
    def test_agent_validation_error_sync(self):
        """Test agent validation error handling synchronously."""
        asyncio.run(self.test_agent_validation_error())
    
    async def test_agent_execution_error(self):
        """Test agent execution error handling."""
        failing_agent = TestFailingAgent()
        
        input_data = {"test_input": "test_value"}
        config = {
            "configurable": {
                "tenant_id": self.test_tenant_id,
                "user_id": self.test_user_id,
            }
        }
        
        with self.assertRaises(AgentExecutionError):
            await failing_agent.invoke(input_data, config)
    
    def test_agent_execution_error_sync(self):
        """Test agent execution error handling synchronously."""
        asyncio.run(self.test_agent_execution_error())
    
    @patch('backend.agents.shared.utils.validation.validate_tenant_access')
    async def test_tenant_validation_middleware(self, mock_validate):
        """Test tenant validation middleware."""
        # Test successful validation
        mock_validate.return_value = True
        
        result = await self.run_agent_test(self.agent)
        self.assert_agent_success(result)
        
        # Test failed validation
        mock_validate.return_value = False
        
        with self.assertRaises(AgentAuthorizationError):
            await self.run_agent_test(self.agent)
    
    def test_tenant_validation_middleware_sync(self):
        """Test tenant validation middleware synchronously."""
        asyncio.run(self.test_tenant_validation_middleware())
    
    async def test_timeout_middleware(self):
        """Test timeout middleware."""
        # Create agent with very short timeout
        context = create_test_context(max_execution_time=0.001)  # 1ms timeout
        state = create_test_state(context)
        
        # Add delay to agent execution
        class SlowAgent(TestAgent):
            async def execute(self, state, config):
                await asyncio.sleep(0.1)  # 100ms delay
                return await super().execute(state, config)
        
        slow_agent = SlowAgent()
        
        with self.assertRaises(AgentTimeoutError):
            await slow_agent(state, {})
    
    def test_timeout_middleware_sync(self):
        """Test timeout middleware synchronously."""
        asyncio.run(self.test_timeout_middleware())
    
    def test_create_graph(self):
        """Test graph creation."""
        graph = self.agent.create_graph()
        
        self.assertIsNotNone(graph)
        # Additional graph validation would go here
    
    def test_get_metrics(self):
        """Test metrics retrieval."""
        metrics = self.agent.get_metrics()
        
        self.assertIsInstance(metrics, dict)
        self.assertIn("agent_name", metrics)
        self.assertIn("agent_type", metrics)
    
    def test_get_health_status(self):
        """Test health status retrieval."""
        health = self.agent.get_health_status()
        
        self.assertIsInstance(health, dict)
        self.assertIn("agent_name", health)
        self.assertIn("agent_type", health)
        self.assertIn("is_executing", health)
        self.assertIn("metrics", health)
    
    def test_agent_configuration(self):
        """Test agent configuration validation."""
        super().test_agent_configuration(self.agent)
    
    def test_tenant_isolation(self):
        """Test tenant isolation enforcement."""
        # This test would verify that agents properly isolate tenant data
        # Implementation depends on specific tenant isolation requirements
        pass
    
    def test_error_handling(self):
        """Test error handling capabilities."""
        # Test that errors are properly caught and handled
        failing_agent = TestFailingAgent()
        
        try:
            asyncio.run(self.run_agent_test(failing_agent))
            self.fail("Expected AgentExecutionError")
        except AgentExecutionError:
            pass  # Expected
    
    def test_performance(self):
        """Test performance characteristics."""
        # Run performance test
        start_time = asyncio.get_event_loop().time()
        asyncio.run(self.run_agent_test(self.agent))
        execution_time = asyncio.get_event_loop().time() - start_time
        
        # Assert reasonable execution time
        self.assert_performance_acceptable(execution_time, max_time=1.0)


class TestMockAgent(BaseAgentTest):
    """Test cases for MockAgent."""
    
    def setUp(self):
        super().setUp()
        self.mock_agent = MockAgent()
    
    def test_mock_agent_basic_functionality(self):
        """Test basic mock agent functionality."""
        result = self.run_agent_test_sync(self.mock_agent)
        
        self.assert_agent_success(result)
        self.assertTrue(result.get("initialized"))
        self.assertTrue(result.get("executed"))
        self.assertTrue(result.get("cleaned_up"))
    
    def test_mock_agent_call_history(self):
        """Test mock agent call history tracking."""
        self.run_agent_test_sync(self.mock_agent)
        
        history = self.mock_agent.get_call_history()
        
        self.assertEqual(len(history["initialize"]), 1)
        self.assertEqual(len(history["execute"]), 1)
        self.assertEqual(len(history["cleanup"]), 1)
    
    def test_mock_agent_failure_simulation(self):
        """Test mock agent failure simulation."""
        self.mock_agent.set_should_fail(True, "Test failure")
        
        with self.assertRaises(AgentExecutionError):
            self.run_agent_test_sync(self.mock_agent)
    
    def test_mock_agent_delay_simulation(self):
        """Test mock agent delay simulation."""
        self.mock_agent.set_execution_delay(0.1)  # 100ms delay
        
        start_time = asyncio.get_event_loop().time()
        self.run_agent_test_sync(self.mock_agent)
        execution_time = asyncio.get_event_loop().time() - start_time
        
        # Should take at least 300ms (100ms * 3 lifecycle methods)
        self.assertGreaterEqual(execution_time, 0.3)


if __name__ == "__main__":
    unittest.main()
