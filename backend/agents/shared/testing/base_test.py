"""
Base test class for agent testing.

This module provides a comprehensive base test class that includes common
testing patterns, utilities, and assertions for agent testing.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type
from unittest.mock import AsyncMock, MagicMock

import pytest

from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState

logger = logging.getLogger(__name__)


class BaseAgentTest:
    """
    Base test class for agent testing.
    
    This class provides common testing utilities, fixtures, and patterns
    for testing agents. It includes state management, mocking, and
    assertion utilities.
    """
    
    # Test configuration
    timeout = 30  # Default timeout for async operations
    performance_threshold = 5.0  # Default performance threshold in seconds
    
    @pytest.fixture(autouse=True)
    def setup_test(self):
        """Set up test environment for each test method."""
        self.start_time = time.time()
        self.test_data = {}
        self.mocks = {}
        
        yield
        
        # Cleanup after test
        self.cleanup_test()
    
    def cleanup_test(self):
        """Clean up test resources."""
        end_time = time.time()
        duration = end_time - self.start_time
        logger.info(f"Test completed in {duration:.2f} seconds")
        
        # Clear test data
        self.test_data.clear()
        self.mocks.clear()
    
    # ========================================================================
    # State Management Utilities
    # ========================================================================
    
    def create_test_state(
        self,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        messages: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create a test state with default values.
        
        Args:
            tenant_id: Tenant ID (generates random if None)
            user_id: User ID (generates random if None)
            thread_id: Thread ID (generates random if None)
            messages: Initial messages
            **kwargs: Additional state fields
            
        Returns:
            AiLexState instance for testing
        """
        state = AiLexState(
            messages=messages or [],
            user_context={
                "user_id": user_id or f"user-{uuid.uuid4()}",
                "tenant_id": tenant_id or f"tenant-{uuid.uuid4()}",
                "role": "attorney",
                "assigned_case_ids": [],
                "settings": {}
            },
            thread_id=thread_id or f"thread-{uuid.uuid4()}",
            **kwargs
        )
        
        return state
    
    def add_test_message(
        self,
        state: AiLexState,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AiLexState:
        """
        Add a test message to the state.
        
        Args:
            state: State to modify
            role: Message role
            content: Message content
            metadata: Optional metadata
            
        Returns:
            Modified state
        """
        state.add_message(role, content, metadata)
        return state
    
    # ========================================================================
    # Mock Management
    # ========================================================================
    
    def create_mock_agent(
        self,
        agent_class: Type[BaseAgent],
        config: Optional[AgentConfig] = None,
        **mock_methods
    ) -> MagicMock:
        """
        Create a mock agent with specified behavior.
        
        Args:
            agent_class: Agent class to mock
            config: Agent configuration
            **mock_methods: Methods to mock with their return values
            
        Returns:
            Mock agent instance
        """
        mock_agent = MagicMock(spec=agent_class)
        
        if config:
            mock_agent.config = config
        else:
            mock_agent.config = AgentConfig(name="MockAgent")
        
        # Set up mock methods
        for method_name, return_value in mock_methods.items():
            if asyncio.iscoroutinefunction(getattr(agent_class, method_name, None)):
                setattr(mock_agent, method_name, AsyncMock(return_value=return_value))
            else:
                setattr(mock_agent, method_name, MagicMock(return_value=return_value))
        
        self.mocks[f"agent_{agent_class.__name__}"] = mock_agent
        return mock_agent
    
    def create_mock_tool_executor(self, responses: Optional[Dict[str, Any]] = None) -> AsyncMock:
        """
        Create a mock tool executor.
        
        Args:
            responses: Tool responses mapping tool_name -> response
            
        Returns:
            Mock tool executor
        """
        mock_executor = AsyncMock()
        
        if responses:
            async def execute_tool(tool_name: str, *args, **kwargs):
                return responses.get(tool_name, {"result": "success"})
            
            mock_executor.execute_tool.side_effect = execute_tool
        else:
            mock_executor.execute_tool.return_value = {"result": "success"}
        
        self.mocks["tool_executor"] = mock_executor
        return mock_executor
    
    # ========================================================================
    # Assertion Utilities
    # ========================================================================
    
    def assert_state_valid(self, state: AiLexState):
        """
        Assert that a state is valid.
        
        Args:
            state: State to validate
        """
        assert isinstance(state, AiLexState), "State must be AiLexState instance"
        assert state.user_context is not None, "State must have user context"
        assert state.thread_id is not None, "State must have thread ID"
        assert isinstance(state.messages, list), "Messages must be a list"
    
    def assert_message_added(
        self,
        state: AiLexState,
        role: str,
        content: Optional[str] = None,
        position: int = -1
    ):
        """
        Assert that a message was added to the state.
        
        Args:
            state: State to check
            role: Expected message role
            content: Expected message content (optional)
            position: Message position (-1 for last)
        """
        assert len(state.messages) > 0, "No messages in state"
        
        message = state.messages[position]
        assert message["role"] == role, f"Expected role {role}, got {message['role']}"
        
        if content:
            assert content in message["content"], f"Content '{content}' not found in message"
    
    def assert_agent_executed(self, mock_agent: MagicMock):
        """
        Assert that an agent was executed.
        
        Args:
            mock_agent: Mock agent to check
        """
        mock_agent.initialize.assert_called_once()
        mock_agent.execute.assert_called_once()
        mock_agent.cleanup.assert_called_once()
    
    def assert_performance_within_threshold(self, duration: float, threshold: Optional[float] = None):
        """
        Assert that operation completed within performance threshold.
        
        Args:
            duration: Operation duration in seconds
            threshold: Performance threshold (uses default if None)
        """
        threshold = threshold or self.performance_threshold
        assert duration <= threshold, f"Operation took {duration:.2f}s, threshold is {threshold}s"
    
    # ========================================================================
    # Async Testing Utilities
    # ========================================================================
    
    async def run_with_timeout(self, coro, timeout: Optional[float] = None):
        """
        Run a coroutine with timeout.
        
        Args:
            coro: Coroutine to run
            timeout: Timeout in seconds (uses default if None)
            
        Returns:
            Coroutine result
        """
        timeout = timeout or self.timeout
        return await asyncio.wait_for(coro, timeout=timeout)
    
    async def measure_performance(self, coro):
        """
        Measure the performance of a coroutine.
        
        Args:
            coro: Coroutine to measure
            
        Returns:
            Tuple of (result, duration_in_seconds)
        """
        start_time = time.time()
        result = await coro
        end_time = time.time()
        duration = end_time - start_time
        
        return result, duration
