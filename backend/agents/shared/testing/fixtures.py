"""
Shared fixtures for agent testing.

This module provides reusable pytest fixtures for agent testing,
including state fixtures, configuration fixtures, and mock fixtures.
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock

import pytest

from shared.core.base_agent import AgentConfig
from shared.core.state import AiLexState


class AgentFixtures:
    """Collection of reusable fixtures for agent testing."""
    
    @staticmethod
    @pytest.fixture
    def agent_config():
        """Create a test agent configuration."""
        return AgentConfig(
            name="TestAgent",
            description="Test agent for unit testing",
            version="1.0.0",
            tools=["test_tool"],
            model="gpt-4o",
            temperature=0.7
        )
    
    @staticmethod
    @pytest.fixture
    def test_tenant_id():
        """Generate a unique tenant ID for testing."""
        return f"tenant-{uuid.uuid4()}"
    
    @staticmethod
    @pytest.fixture
    def test_user_id():
        """Generate a unique user ID for testing."""
        return f"user-{uuid.uuid4()}"
    
    @staticmethod
    @pytest.fixture
    def test_thread_id():
        """Generate a unique thread ID for testing."""
        return f"thread-{uuid.uuid4()}"
    
    @staticmethod
    @pytest.fixture
    def test_user_context(test_tenant_id, test_user_id):
        """Create a test user context."""
        return {
            "user_id": test_user_id,
            "tenant_id": test_tenant_id,
            "role": "attorney",
            "assigned_case_ids": [f"case-{uuid.uuid4()}" for _ in range(3)],
            "settings": {
                "timezone": "UTC",
                "language": "en",
                "notifications": True
            }
        }
    
    @staticmethod
    @pytest.fixture
    def empty_state(test_tenant_id, test_user_id, test_thread_id, test_user_context):
        """Create an empty test state."""
        return AiLexState(
            messages=[],
            user_context=test_user_context,
            thread_id=test_thread_id
        )
    
    @staticmethod
    @pytest.fixture
    def state_with_messages(empty_state):
        """Create a test state with sample messages."""
        empty_state.add_message("user", "Hello, I need help with my case.")
        empty_state.add_message("assistant", "I'd be happy to help you with your case. What specific information do you need?")
        empty_state.add_message("user", "I need to know about the statute of limitations.")
        return empty_state
    
    @staticmethod
    @pytest.fixture
    def runnable_config(test_tenant_id, test_user_id, test_thread_id):
        """Create a runnable configuration for LangGraph."""
        return {
            "configurable": {
                "tenant_id": test_tenant_id,
                "user_id": test_user_id,
                "thread_id": test_thread_id
            },
            "metadata": {
                "test": True,
                "created_at": datetime.utcnow().isoformat()
            }
        }
    
    @staticmethod
    @pytest.fixture
    def mock_llm_response():
        """Create a mock LLM response."""
        return {
            "content": "This is a test response from the LLM.",
            "role": "assistant",
            "metadata": {
                "model": "gpt-4o",
                "tokens_used": 50,
                "finish_reason": "stop"
            }
        }
    
    @staticmethod
    @pytest.fixture
    def mock_tool_response():
        """Create a mock tool response."""
        return {
            "result": "success",
            "data": {
                "documents": [
                    {"id": "doc1", "title": "Test Document 1", "content": "Sample content 1"},
                    {"id": "doc2", "title": "Test Document 2", "content": "Sample content 2"}
                ]
            },
            "metadata": {
                "execution_time": 0.5,
                "tool_name": "test_tool"
            }
        }
    
    @staticmethod
    @pytest.fixture
    def mock_database_session():
        """Create a mock database session."""
        session = MagicMock()
        session.query.return_value.filter.return_value.first.return_value = None
        session.query.return_value.filter.return_value.all.return_value = []
        session.add = MagicMock()
        session.commit = MagicMock()
        session.rollback = MagicMock()
        session.close = MagicMock()
        return session
    
    @staticmethod
    @pytest.fixture
    def mock_external_apis():
        """Create mocks for external APIs."""
        return {
            "supabase": MagicMock(),
            "pinecone": MagicMock(),
            "openai": MagicMock(),
            "voyage": MagicMock()
        }
    
    @staticmethod
    @pytest.fixture
    def performance_config():
        """Create performance testing configuration."""
        return {
            "max_execution_time": 5.0,
            "max_memory_usage": 100 * 1024 * 1024,  # 100MB
            "max_concurrent_operations": 10,
            "timeout": 30.0
        }
    
    @staticmethod
    @pytest.fixture
    def test_documents():
        """Create test documents for retrieval testing."""
        return [
            {
                "id": f"doc-{i}",
                "title": f"Test Document {i}",
                "content": f"This is the content of test document {i}. It contains legal information about topic {i}.",
                "metadata": {
                    "type": "legal_document",
                    "jurisdiction": "Texas",
                    "practice_area": "personal_injury",
                    "created_at": (datetime.utcnow() - timedelta(days=i)).isoformat()
                }
            }
            for i in range(1, 6)
        ]
    
    @staticmethod
    @pytest.fixture
    def test_cases():
        """Create test cases for case management testing."""
        return [
            {
                "id": f"case-{i}",
                "title": f"Test Case {i}",
                "description": f"Description for test case {i}",
                "status": "active" if i % 2 == 0 else "closed",
                "practice_area": "personal_injury",
                "client_id": f"client-{i}",
                "created_at": (datetime.utcnow() - timedelta(days=i * 10)).isoformat(),
                "updated_at": (datetime.utcnow() - timedelta(days=i)).isoformat()
            }
            for i in range(1, 4)
        ]
    
    @staticmethod
    @pytest.fixture
    def test_tasks():
        """Create test tasks for task management testing."""
        return [
            {
                "id": f"task-{i}",
                "title": f"Test Task {i}",
                "description": f"Description for test task {i}",
                "status": "todo" if i == 1 else "in_progress" if i == 2 else "done",
                "priority": "high" if i == 1 else "medium" if i == 2 else "low",
                "assignee_id": f"user-{i}",
                "case_id": f"case-{i}",
                "due_date": (datetime.utcnow() + timedelta(days=i * 7)).isoformat(),
                "created_at": (datetime.utcnow() - timedelta(days=i)).isoformat()
            }
            for i in range(1, 4)
        ]
    
    @staticmethod
    @pytest.fixture
    def error_scenarios():
        """Create error scenarios for error handling testing."""
        return {
            "network_error": Exception("Network connection failed"),
            "timeout_error": TimeoutError("Operation timed out"),
            "validation_error": ValueError("Invalid input parameters"),
            "authentication_error": PermissionError("Authentication failed"),
            "not_found_error": FileNotFoundError("Resource not found"),
            "rate_limit_error": Exception("Rate limit exceeded")
        }
