"""
Custom assertions for agent testing.

This module provides specialized assertion functions for testing agents,
including state validation, message checking, and performance assertions.
"""

import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from unittest.mock import MagicMock

from shared.core.state import AiLexState


class AgentAssertions:
    """Custom assertions for agent testing."""
    
    # ========================================================================
    # State Assertions
    # ========================================================================
    
    @staticmethod
    def assert_state_valid(state: AiLexState, message: str = "State validation failed"):
        """
        Assert that a state is valid and properly structured.
        
        Args:
            state: State to validate
            message: Custom error message
        """
        assert isinstance(state, AiLexState), f"{message}: State must be AiLexState instance"
        assert state.user_context is not None, f"{message}: State must have user context"
        assert state.thread_id is not None, f"{message}: State must have thread ID"
        assert isinstance(state.messages, list), f"{message}: Messages must be a list"
        
        # Validate user context structure
        user_context = state.user_context
        assert "user_id" in user_context, f"{message}: User context must have user_id"
        assert "tenant_id" in user_context, f"{message}: User context must have tenant_id"
        assert "role" in user_context, f"{message}: User context must have role"
    
    @staticmethod
    def assert_state_unchanged(
        original_state: AiLexState,
        current_state: AiLexState,
        exclude_fields: Optional[List[str]] = None
    ):
        """
        Assert that state hasn't changed except for specified fields.
        
        Args:
            original_state: Original state
            current_state: Current state
            exclude_fields: Fields to exclude from comparison
        """
        exclude_fields = exclude_fields or []
        
        # Compare basic fields
        if "messages" not in exclude_fields:
            assert len(original_state.messages) == len(current_state.messages), \
                "Message count changed unexpectedly"
        
        if "user_context" not in exclude_fields:
            assert original_state.user_context == current_state.user_context, \
                "User context changed unexpectedly"
        
        if "thread_id" not in exclude_fields:
            assert original_state.thread_id == current_state.thread_id, \
                "Thread ID changed unexpectedly"
    
    @staticmethod
    def assert_state_has_field(state: AiLexState, field_name: str, expected_value: Any = None):
        """
        Assert that state has a specific field with optional value check.
        
        Args:
            state: State to check
            field_name: Field name to check
            expected_value: Expected field value (optional)
        """
        assert hasattr(state, field_name), f"State missing field: {field_name}"
        
        if expected_value is not None:
            actual_value = getattr(state, field_name)
            assert actual_value == expected_value, \
                f"Field {field_name}: expected {expected_value}, got {actual_value}"
    
    # ========================================================================
    # Message Assertions
    # ========================================================================
    
    @staticmethod
    def assert_message_count(state: AiLexState, expected_count: int):
        """
        Assert the number of messages in state.
        
        Args:
            state: State to check
            expected_count: Expected message count
        """
        actual_count = len(state.messages)
        assert actual_count == expected_count, \
            f"Expected {expected_count} messages, got {actual_count}"
    
    @staticmethod
    def assert_message_added(
        state: AiLexState,
        role: str,
        content: Optional[str] = None,
        position: int = -1,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Assert that a specific message was added to the state.
        
        Args:
            state: State to check
            role: Expected message role
            content: Expected message content (partial match)
            position: Message position (-1 for last)
            metadata: Expected metadata fields
        """
        assert len(state.messages) > 0, "No messages in state"
        
        if position < 0:
            position = len(state.messages) + position
        
        assert position < len(state.messages), f"Position {position} out of range"
        
        message = state.messages[position]
        assert message["role"] == role, f"Expected role {role}, got {message['role']}"
        
        if content:
            assert content in message["content"], \
                f"Content '{content}' not found in message: {message['content']}"
        
        if metadata:
            message_metadata = message.get("metadata", {})
            for key, expected_value in metadata.items():
                assert key in message_metadata, f"Metadata key '{key}' not found"
                assert message_metadata[key] == expected_value, \
                    f"Metadata {key}: expected {expected_value}, got {message_metadata[key]}"
    
    @staticmethod
    def assert_message_contains(state: AiLexState, content: str, role: Optional[str] = None):
        """
        Assert that any message contains specific content.
        
        Args:
            state: State to check
            content: Content to search for
            role: Optional role filter
        """
        messages = state.messages
        if role:
            messages = [msg for msg in messages if msg["role"] == role]
        
        found = any(content in msg["content"] for msg in messages)
        assert found, f"Content '{content}' not found in any message"
    
    @staticmethod
    def assert_no_error_messages(state: AiLexState):
        """
        Assert that state contains no error messages.
        
        Args:
            state: State to check
        """
        error_indicators = ["error", "failed", "exception", "Error:", "Failed:"]
        
        for message in state.messages:
            content = message["content"].lower()
            metadata = message.get("metadata", {})
            
            # Check content for error indicators
            for indicator in error_indicators:
                assert indicator.lower() not in content, \
                    f"Error indicator '{indicator}' found in message: {message['content']}"
            
            # Check metadata for error flags
            assert not metadata.get("error", False), \
                f"Error flag found in message metadata: {message}"
    
    # ========================================================================
    # Agent Execution Assertions
    # ========================================================================
    
    @staticmethod
    def assert_agent_called(mock_agent: MagicMock, method: str = "execute"):
        """
        Assert that an agent method was called.
        
        Args:
            mock_agent: Mock agent to check
            method: Method name to check
        """
        mock_method = getattr(mock_agent, method)
        assert mock_method.called, f"Agent method '{method}' was not called"
    
    @staticmethod
    def assert_agent_lifecycle_completed(mock_agent: MagicMock):
        """
        Assert that agent completed full lifecycle (initialize, execute, cleanup).
        
        Args:
            mock_agent: Mock agent to check
        """
        assert mock_agent.initialize.called, "Agent initialize method was not called"
        assert mock_agent.execute.called, "Agent execute method was not called"
        assert mock_agent.cleanup.called, "Agent cleanup method was not called"
        
        # Check call order
        initialize_call_time = mock_agent.initialize.call_args_list[0] if mock_agent.initialize.call_args_list else None
        execute_call_time = mock_agent.execute.call_args_list[0] if mock_agent.execute.call_args_list else None
        cleanup_call_time = mock_agent.cleanup.call_args_list[0] if mock_agent.cleanup.call_args_list else None
        
        assert initialize_call_time is not None, "Initialize was not called"
        assert execute_call_time is not None, "Execute was not called"
        assert cleanup_call_time is not None, "Cleanup was not called"
    
    @staticmethod
    def assert_tool_executed(mock_executor: MagicMock, tool_name: str, call_count: int = 1):
        """
        Assert that a tool was executed.
        
        Args:
            mock_executor: Mock tool executor
            tool_name: Tool name to check
            call_count: Expected number of calls
        """
        assert mock_executor.execute_tool.called, "Tool executor was not called"
        
        # Check if specific tool was called
        calls = mock_executor.execute_tool.call_args_list
        tool_calls = [call for call in calls if call[0][0] == tool_name]
        
        assert len(tool_calls) == call_count, \
            f"Tool '{tool_name}' called {len(tool_calls)} times, expected {call_count}"
    
    # ========================================================================
    # Performance Assertions
    # ========================================================================
    
    @staticmethod
    def assert_execution_time(duration: float, max_duration: float):
        """
        Assert that execution completed within time limit.
        
        Args:
            duration: Actual execution duration
            max_duration: Maximum allowed duration
        """
        assert duration <= max_duration, \
            f"Execution took {duration:.2f}s, maximum allowed is {max_duration:.2f}s"
    
    @staticmethod
    def assert_memory_usage(memory_usage: int, max_memory: int):
        """
        Assert that memory usage is within limits.
        
        Args:
            memory_usage: Actual memory usage in bytes
            max_memory: Maximum allowed memory in bytes
        """
        assert memory_usage <= max_memory, \
            f"Memory usage {memory_usage} bytes exceeds limit of {max_memory} bytes"
    
    @staticmethod
    def assert_response_time_acceptable(start_time: float, max_response_time: float = 5.0):
        """
        Assert that response time is acceptable.
        
        Args:
            start_time: Operation start time
            max_response_time: Maximum acceptable response time
        """
        duration = time.time() - start_time
        assert duration <= max_response_time, \
            f"Response time {duration:.2f}s exceeds acceptable limit of {max_response_time:.2f}s"
    
    # ========================================================================
    # Data Validation Assertions
    # ========================================================================
    
    @staticmethod
    def assert_tenant_isolation(state: AiLexState, expected_tenant_id: str):
        """
        Assert that state maintains proper tenant isolation.
        
        Args:
            state: State to check
            expected_tenant_id: Expected tenant ID
        """
        assert state.user_context["tenant_id"] == expected_tenant_id, \
            f"Tenant isolation violated: expected {expected_tenant_id}, got {state.user_context['tenant_id']}"
    
    @staticmethod
    def assert_user_context_valid(user_context: Dict[str, Any]):
        """
        Assert that user context is valid and complete.
        
        Args:
            user_context: User context to validate
        """
        required_fields = ["user_id", "tenant_id", "role"]
        
        for field in required_fields:
            assert field in user_context, f"User context missing required field: {field}"
            assert user_context[field] is not None, f"User context field {field} is None"
            assert user_context[field] != "", f"User context field {field} is empty"
    
    @staticmethod
    def assert_configuration_valid(config: Dict[str, Any], required_fields: List[str]):
        """
        Assert that configuration is valid and complete.
        
        Args:
            config: Configuration to validate
            required_fields: List of required field names
        """
        for field in required_fields:
            assert field in config, f"Configuration missing required field: {field}"
            assert config[field] is not None, f"Configuration field {field} is None"
