"""
Advanced testing utilities for complex agent testing scenarios.

This module provides sophisticated testing tools for advanced scenarios
including load testing, chaos engineering, and complex workflow validation.
"""

import asyncio
import random
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Tuple
from unittest.mock import AsyncMock, MagicMock

import pytest


@dataclass
class LoadTestConfig:
    """Configuration for load testing."""
    concurrent_users: int = 10
    requests_per_user: int = 5
    ramp_up_time: float = 1.0
    test_duration: float = 30.0
    think_time_min: float = 0.1
    think_time_max: float = 0.5


@dataclass
class ChaosConfig:
    """Configuration for chaos engineering tests."""
    failure_rate: float = 0.1
    latency_injection: bool = True
    latency_min: float = 0.1
    latency_max: float = 2.0
    memory_pressure: bool = False
    network_partition: bool = False


class LoadTester:
    """Advanced load testing utility for agent systems."""
    
    def __init__(self, config: LoadTestConfig):
        self.config = config
        self.results = []
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    async def run_load_test(
        self,
        target_function: Callable,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Run a comprehensive load test against a target function.
        
        Args:
            target_function: Function to test under load
            *args: Arguments for target function
            **kwargs: Keyword arguments for target function
            
        Returns:
            Load test results dictionary
        """
        self.start_time = time.time()
        
        # Create user tasks
        user_tasks = []
        for user_id in range(self.config.concurrent_users):
            task = asyncio.create_task(
                self._simulate_user(user_id, target_function, *args, **kwargs)
            )
            user_tasks.append(task)
            
            # Ramp up delay
            if self.config.ramp_up_time > 0:
                await asyncio.sleep(self.config.ramp_up_time / self.config.concurrent_users)
        
        # Wait for all users to complete
        await asyncio.gather(*user_tasks, return_exceptions=True)
        
        self.end_time = time.time()
        
        return self._calculate_results()
    
    async def _simulate_user(
        self,
        user_id: int,
        target_function: Callable,
        *args,
        **kwargs
    ):
        """Simulate a single user's behavior."""
        user_results = []
        
        for request_id in range(self.config.requests_per_user):
            start_time = time.time()
            
            try:
                # Execute target function
                if asyncio.iscoroutinefunction(target_function):
                    result = await target_function(*args, **kwargs)
                else:
                    result = target_function(*args, **kwargs)
                
                end_time = time.time()
                
                user_results.append({
                    "user_id": user_id,
                    "request_id": request_id,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "success": True,
                    "result": result
                })
                
            except Exception as e:
                end_time = time.time()
                
                self.errors.append({
                    "user_id": user_id,
                    "request_id": request_id,
                    "error": str(e),
                    "timestamp": end_time
                })
                
                user_results.append({
                    "user_id": user_id,
                    "request_id": request_id,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "success": False,
                    "error": str(e)
                })
            
            # Think time between requests
            think_time = random.uniform(
                self.config.think_time_min,
                self.config.think_time_max
            )
            await asyncio.sleep(think_time)
        
        self.results.extend(user_results)
    
    def _calculate_results(self) -> Dict[str, Any]:
        """Calculate comprehensive load test results."""
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r["success"])
        failed_requests = total_requests - successful_requests
        
        durations = [r["duration"] for r in self.results if r["success"]]
        
        if durations:
            avg_response_time = sum(durations) / len(durations)
            min_response_time = min(durations)
            max_response_time = max(durations)
            
            # Calculate percentiles
            sorted_durations = sorted(durations)
            p50 = sorted_durations[int(len(sorted_durations) * 0.5)]
            p90 = sorted_durations[int(len(sorted_durations) * 0.9)]
            p95 = sorted_durations[int(len(sorted_durations) * 0.95)]
            p99 = sorted_durations[int(len(sorted_durations) * 0.99)]
        else:
            avg_response_time = 0
            min_response_time = 0
            max_response_time = 0
            p50 = p90 = p95 = p99 = 0
        
        total_duration = self.end_time - self.start_time
        throughput = total_requests / total_duration if total_duration > 0 else 0
        
        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": successful_requests / total_requests if total_requests > 0 else 0,
            "total_duration": total_duration,
            "throughput": throughput,
            "avg_response_time": avg_response_time,
            "min_response_time": min_response_time,
            "max_response_time": max_response_time,
            "percentiles": {
                "p50": p50,
                "p90": p90,
                "p95": p95,
                "p99": p99
            },
            "errors": self.errors,
            "config": {
                "concurrent_users": self.config.concurrent_users,
                "requests_per_user": self.config.requests_per_user,
                "ramp_up_time": self.config.ramp_up_time
            }
        }


class ChaosEngineer:
    """Chaos engineering utility for testing system resilience."""
    
    def __init__(self, config: ChaosConfig):
        self.config = config
        self.active_failures = []
    
    @asynccontextmanager
    async def inject_chaos(self):
        """Context manager for injecting chaos into tests."""
        try:
            await self._start_chaos()
            yield self
        finally:
            await self._stop_chaos()
    
    async def _start_chaos(self):
        """Start chaos injection."""
        if self.config.latency_injection:
            self.active_failures.append("latency")
        
        if self.config.memory_pressure:
            self.active_failures.append("memory")
        
        if self.config.network_partition:
            self.active_failures.append("network")
    
    async def _stop_chaos(self):
        """Stop chaos injection."""
        self.active_failures.clear()
    
    async def maybe_inject_failure(self, operation_name: str):
        """Maybe inject a failure based on configuration."""
        if random.random() < self.config.failure_rate:
            failure_type = random.choice(["latency", "error", "timeout"])
            
            if failure_type == "latency" and self.config.latency_injection:
                delay = random.uniform(self.config.latency_min, self.config.latency_max)
                await asyncio.sleep(delay)
            
            elif failure_type == "error":
                raise Exception(f"Chaos-injected error in {operation_name}")
            
            elif failure_type == "timeout":
                await asyncio.sleep(10)  # Simulate timeout
    
    def create_chaotic_mock(self, original_mock: MagicMock) -> MagicMock:
        """Create a mock that exhibits chaotic behavior."""
        async def chaotic_side_effect(*args, **kwargs):
            await self.maybe_inject_failure("mock_operation")
            
            if hasattr(original_mock, 'return_value'):
                return original_mock.return_value
            else:
                return {"result": "success"}
        
        chaotic_mock = AsyncMock(side_effect=chaotic_side_effect)
        return chaotic_mock


class WorkflowValidator:
    """Advanced workflow validation utility."""
    
    def __init__(self):
        self.validation_rules = []
        self.execution_trace = []
    
    def add_validation_rule(self, rule_name: str, validator: Callable):
        """Add a validation rule for workflow execution."""
        self.validation_rules.append({
            "name": rule_name,
            "validator": validator
        })
    
    def trace_execution(self, step_name: str, data: Any):
        """Trace workflow execution for validation."""
        self.execution_trace.append({
            "step": step_name,
            "data": data,
            "timestamp": time.time()
        })
    
    def validate_workflow(self) -> Dict[str, Any]:
        """Validate the entire workflow execution."""
        validation_results = {
            "passed": True,
            "results": [],
            "trace": self.execution_trace.copy()
        }
        
        for rule in self.validation_rules:
            try:
                result = rule["validator"](self.execution_trace)
                validation_results["results"].append({
                    "rule": rule["name"],
                    "passed": result,
                    "error": None
                })
                
                if not result:
                    validation_results["passed"] = False
                    
            except Exception as e:
                validation_results["results"].append({
                    "rule": rule["name"],
                    "passed": False,
                    "error": str(e)
                })
                validation_results["passed"] = False
        
        return validation_results
    
    def clear_trace(self):
        """Clear the execution trace."""
        self.execution_trace.clear()


class TestScenarioRunner:
    """Advanced test scenario runner for complex test cases."""
    
    def __init__(self):
        self.scenarios = []
        self.results = []
    
    def add_scenario(
        self,
        name: str,
        setup_func: Callable,
        test_func: Callable,
        teardown_func: Optional[Callable] = None,
        expected_outcome: str = "success"
    ):
        """Add a test scenario."""
        self.scenarios.append({
            "name": name,
            "setup": setup_func,
            "test": test_func,
            "teardown": teardown_func,
            "expected_outcome": expected_outcome
        })
    
    async def run_all_scenarios(self) -> List[Dict[str, Any]]:
        """Run all test scenarios."""
        self.results.clear()
        
        for scenario in self.scenarios:
            result = await self._run_scenario(scenario)
            self.results.append(result)
        
        return self.results
    
    async def _run_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test scenario."""
        start_time = time.time()
        result = {
            "name": scenario["name"],
            "start_time": start_time,
            "success": False,
            "error": None,
            "duration": 0,
            "expected_outcome": scenario["expected_outcome"]
        }
        
        try:
            # Setup
            if scenario["setup"]:
                setup_result = scenario["setup"]()
                if asyncio.iscoroutine(setup_result):
                    await setup_result
            
            # Execute test
            test_result = scenario["test"]()
            if asyncio.iscoroutine(test_result):
                test_result = await test_result
            
            # Validate outcome
            if scenario["expected_outcome"] == "success":
                result["success"] = True
            elif scenario["expected_outcome"] == "failure":
                result["success"] = False
                result["error"] = "Expected failure but test succeeded"
            
            result["test_result"] = test_result
            
        except Exception as e:
            if scenario["expected_outcome"] == "failure":
                result["success"] = True
                result["error"] = f"Expected failure: {str(e)}"
            else:
                result["success"] = False
                result["error"] = str(e)
        
        finally:
            # Teardown
            if scenario["teardown"]:
                try:
                    teardown_result = scenario["teardown"]()
                    if asyncio.iscoroutine(teardown_result):
                        await teardown_result
                except Exception as e:
                    result["teardown_error"] = str(e)
            
            result["duration"] = time.time() - start_time
        
        return result


# Pytest fixtures for advanced utilities
@pytest.fixture
def load_tester():
    """Create a load tester with default configuration."""
    config = LoadTestConfig(concurrent_users=5, requests_per_user=3)
    return LoadTester(config)


@pytest.fixture
def chaos_engineer():
    """Create a chaos engineer with default configuration."""
    config = ChaosConfig(failure_rate=0.2, latency_injection=True)
    return ChaosEngineer(config)


@pytest.fixture
def workflow_validator():
    """Create a workflow validator."""
    return WorkflowValidator()


@pytest.fixture
def scenario_runner():
    """Create a test scenario runner."""
    return TestScenarioRunner()
