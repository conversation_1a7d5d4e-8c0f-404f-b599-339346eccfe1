"""
Factory classes for creating test data and objects.

This module provides factory classes for creating consistent test data,
including states, configurations, and mock objects for agent testing.
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type
from unittest.mock import MagicMock

from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState


class StateFactory:
    """Factory for creating test states with various configurations."""
    
    @staticmethod
    def create_empty_state(
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create an empty state with minimal required fields.
        
        Args:
            tenant_id: Tenant ID (generates random if None)
            user_id: User ID (generates random if None)
            thread_id: Thread ID (generates random if None)
            **kwargs: Additional state fields
            
        Returns:
            Empty AiLexState instance
        """
        tenant_id = tenant_id or f"tenant-{uuid.uuid4()}"
        user_id = user_id or f"user-{uuid.uuid4()}"
        thread_id = thread_id or f"thread-{uuid.uuid4()}"
        
        user_context = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "role": "attorney",
            "assigned_case_ids": [],
            "settings": {}
        }
        
        return AiLexState(
            messages=[],
            user_context=user_context,
            thread_id=thread_id,
            **kwargs
        )
    
    @staticmethod
    def create_state_with_messages(
        messages: List[Dict[str, Any]],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create a state with predefined messages.
        
        Args:
            messages: List of message dictionaries
            tenant_id: Tenant ID
            user_id: User ID
            **kwargs: Additional state fields
            
        Returns:
            AiLexState with messages
        """
        state = StateFactory.create_empty_state(tenant_id, user_id, **kwargs)
        
        for message in messages:
            state.add_message(
                role=message["role"],
                content=message["content"],
                metadata=message.get("metadata")
            )
        
        return state
    
    @staticmethod
    def create_conversation_state(
        conversation_length: int = 3,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create a state with a realistic conversation.
        
        Args:
            conversation_length: Number of message pairs
            tenant_id: Tenant ID
            user_id: User ID
            **kwargs: Additional state fields
            
        Returns:
            AiLexState with conversation
        """
        state = StateFactory.create_empty_state(tenant_id, user_id, **kwargs)
        
        conversation_templates = [
            ("user", "Hello, I need help with my legal case."),
            ("assistant", "I'd be happy to help you with your legal case. What specific information do you need?"),
            ("user", "I need to understand the statute of limitations for personal injury in Texas."),
            ("assistant", "In Texas, the statute of limitations for personal injury cases is generally 2 years from the date of injury."),
            ("user", "What documents do I need to file a claim?"),
            ("assistant", "For a personal injury claim, you'll typically need medical records, incident reports, witness statements, and documentation of damages."),
        ]
        
        for i in range(min(conversation_length * 2, len(conversation_templates))):
            role, content = conversation_templates[i]
            state.add_message(role, content)
        
        return state
    
    @staticmethod
    def create_error_state(
        error_message: str = "Test error occurred",
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ) -> AiLexState:
        """
        Create a state with an error message.
        
        Args:
            error_message: Error message content
            tenant_id: Tenant ID
            user_id: User ID
            **kwargs: Additional state fields
            
        Returns:
            AiLexState with error message
        """
        state = StateFactory.create_empty_state(tenant_id, user_id, **kwargs)
        state.add_message("system", error_message, {"error": True})
        return state
    
    @staticmethod
    def create_multi_tenant_states(
        tenant_count: int = 3,
        messages_per_tenant: int = 2
    ) -> List[AiLexState]:
        """
        Create multiple states for different tenants.
        
        Args:
            tenant_count: Number of tenants
            messages_per_tenant: Messages per tenant state
            
        Returns:
            List of AiLexState instances for different tenants
        """
        states = []
        
        for i in range(tenant_count):
            tenant_id = f"tenant-{i+1}"
            user_id = f"user-{i+1}"
            
            state = StateFactory.create_empty_state(tenant_id, user_id)
            
            for j in range(messages_per_tenant):
                state.add_message("user", f"Message {j+1} from tenant {i+1}")
                state.add_message("assistant", f"Response {j+1} for tenant {i+1}")
            
            states.append(state)
        
        return states


class AgentFactory:
    """Factory for creating test agents and configurations."""
    
    @staticmethod
    def create_agent_config(
        name: str = "TestAgent",
        description: str = "Test agent for unit testing",
        tools: Optional[List[str]] = None,
        model: str = "gpt-4o",
        temperature: float = 0.7,
        **kwargs
    ) -> AgentConfig:
        """
        Create an agent configuration for testing.
        
        Args:
            name: Agent name
            description: Agent description
            tools: List of tool names
            model: LLM model name
            temperature: LLM temperature
            **kwargs: Additional config fields
            
        Returns:
            AgentConfig instance
        """
        return AgentConfig(
            name=name,
            description=description,
            tools=tools or ["test_tool"],
            model=model,
            temperature=temperature,
            **kwargs
        )
    
    @staticmethod
    def create_mock_agent(
        config: Optional[AgentConfig] = None,
        behavior: Optional[Dict[str, Any]] = None
    ) -> MagicMock:
        """
        Create a mock agent with specified behavior.
        
        Args:
            config: Agent configuration
            behavior: Behavior specification for mock methods
            
        Returns:
            Mock agent instance
        """
        mock_agent = MagicMock(spec=BaseAgent)
        mock_agent.config = config or AgentFactory.create_agent_config()
        
        behavior = behavior or {}
        
        # Set up default behaviors
        default_behavior = {
            "initialize": lambda state, config: state,
            "execute": lambda state, config: state,
            "cleanup": lambda state, config: state
        }
        
        for method_name, default_func in default_behavior.items():
            if method_name in behavior:
                setattr(mock_agent, method_name, behavior[method_name])
            else:
                setattr(mock_agent, method_name, MagicMock(side_effect=default_func))
        
        return mock_agent
    
    @staticmethod
    def create_agent_configs_batch(
        count: int = 3,
        base_name: str = "TestAgent"
    ) -> List[AgentConfig]:
        """
        Create multiple agent configurations.
        
        Args:
            count: Number of configurations to create
            base_name: Base name for agents
            
        Returns:
            List of AgentConfig instances
        """
        configs = []
        
        for i in range(count):
            config = AgentFactory.create_agent_config(
                name=f"{base_name}{i+1}",
                description=f"Test agent {i+1} for unit testing",
                tools=[f"tool_{i+1}"],
                temperature=0.1 * (i + 1)
            )
            configs.append(config)
        
        return configs


class TestDataFactory:
    """Factory for creating various test data structures."""
    
    @staticmethod
    def create_test_documents(count: int = 5) -> List[Dict[str, Any]]:
        """
        Create test documents for retrieval testing.
        
        Args:
            count: Number of documents to create
            
        Returns:
            List of document dictionaries
        """
        documents = []
        
        for i in range(count):
            doc = {
                "id": f"doc-{i+1}",
                "title": f"Test Document {i+1}",
                "content": f"This is the content of test document {i+1}. It contains legal information about topic {i+1}.",
                "metadata": {
                    "type": "legal_document",
                    "jurisdiction": "Texas",
                    "practice_area": "personal_injury",
                    "created_at": (datetime.utcnow() - timedelta(days=i)).isoformat(),
                    "relevance_score": 0.9 - (i * 0.1)
                }
            }
            documents.append(doc)
        
        return documents
    
    @staticmethod
    def create_test_cases(count: int = 3) -> List[Dict[str, Any]]:
        """
        Create test cases for case management testing.
        
        Args:
            count: Number of cases to create
            
        Returns:
            List of case dictionaries
        """
        cases = []
        statuses = ["active", "pending", "closed"]
        
        for i in range(count):
            case = {
                "id": f"case-{i+1}",
                "title": f"Test Case {i+1}",
                "description": f"Description for test case {i+1}",
                "status": statuses[i % len(statuses)],
                "practice_area": "personal_injury",
                "client_id": f"client-{i+1}",
                "attorney_id": f"attorney-{i+1}",
                "created_at": (datetime.utcnow() - timedelta(days=i * 10)).isoformat(),
                "updated_at": (datetime.utcnow() - timedelta(days=i)).isoformat(),
                "metadata": {
                    "priority": "high" if i == 0 else "medium" if i == 1 else "low",
                    "estimated_value": 10000 * (i + 1),
                    "complexity": "simple" if i % 2 == 0 else "complex"
                }
            }
            cases.append(case)
        
        return cases
    
    @staticmethod
    def create_test_tasks(count: int = 5) -> List[Dict[str, Any]]:
        """
        Create test tasks for task management testing.
        
        Args:
            count: Number of tasks to create
            
        Returns:
            List of task dictionaries
        """
        tasks = []
        statuses = ["todo", "in_progress", "done"]
        priorities = ["low", "medium", "high", "urgent"]
        
        for i in range(count):
            task = {
                "id": f"task-{i+1}",
                "title": f"Test Task {i+1}",
                "description": f"Description for test task {i+1}",
                "status": statuses[i % len(statuses)],
                "priority": priorities[i % len(priorities)],
                "assignee_id": f"user-{i+1}",
                "case_id": f"case-{(i % 3) + 1}",
                "due_date": (datetime.utcnow() + timedelta(days=i * 7)).isoformat(),
                "created_at": (datetime.utcnow() - timedelta(days=i)).isoformat(),
                "metadata": {
                    "estimated_hours": (i + 1) * 2,
                    "category": "research" if i % 2 == 0 else "documentation",
                    "tags": [f"tag{i+1}", "test"]
                }
            }
            tasks.append(task)
        
        return tasks
    
    @staticmethod
    def create_performance_test_data(
        operation_count: int = 100,
        data_size: str = "small"
    ) -> Dict[str, Any]:
        """
        Create test data for performance testing.
        
        Args:
            operation_count: Number of operations to simulate
            data_size: Size of test data ("small", "medium", "large")
            
        Returns:
            Performance test data dictionary
        """
        size_multipliers = {"small": 1, "medium": 10, "large": 100}
        multiplier = size_multipliers.get(data_size, 1)
        
        return {
            "operations": [
                {
                    "id": f"op-{i+1}",
                    "type": "test_operation",
                    "data": "x" * (100 * multiplier),
                    "timestamp": (datetime.utcnow() - timedelta(seconds=i)).isoformat()
                }
                for i in range(operation_count)
            ],
            "metadata": {
                "data_size": data_size,
                "operation_count": operation_count,
                "total_data_size": operation_count * 100 * multiplier
            }
        }
