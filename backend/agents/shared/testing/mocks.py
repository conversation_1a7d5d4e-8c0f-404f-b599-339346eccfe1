"""
Mock utilities for agent testing.

This module provides comprehensive mock implementations for external
dependencies, services, and components used in agent testing.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from unittest.mock import AsyncMock, MagicMock

from shared.core.base_agent import BaseAgent, AgentConfig
from shared.core.state import AiLexState


class MockManager:
    """
    Centralized mock management for agent testing.
    
    This class provides a unified interface for creating and managing
    mocks for various components used in agent testing.
    """
    
    def __init__(self):
        self.mocks = {}
        self.active_patches = []
    
    def reset_all(self):
        """Reset all mocks to their initial state."""
        for mock in self.mocks.values():
            if hasattr(mock, 'reset_mock'):
                mock.reset_mock()
    
    def cleanup(self):
        """Clean up all mocks and patches."""
        for patch in self.active_patches:
            patch.stop()
        self.active_patches.clear()
        self.mocks.clear()
    
    # ========================================================================
    # Agent Mocks
    # ========================================================================
    
    def create_mock_agent(
        self,
        name: str = "MockAgent",
        config: Optional[AgentConfig] = None
    ) -> MagicMock:
        """
        Create a mock agent with realistic behavior.
        
        Args:
            name: Agent name
            config: Agent configuration
            
        Returns:
            Mock agent instance
        """
        mock_agent = MagicMock(spec=BaseAgent)
        mock_agent.config = config or AgentConfig(name=name)
        mock_agent.name = name
        
        # Mock lifecycle methods
        async def mock_initialize(state, config=None):
            state.add_message("system", f"{name} initialized")
            return state
        
        async def mock_execute(state, config=None):
            state.add_message("assistant", f"{name} executed successfully")
            return state
        
        async def mock_cleanup(state, config=None):
            state.add_message("system", f"{name} cleanup completed")
            return state
        
        mock_agent.initialize = AsyncMock(side_effect=mock_initialize)
        mock_agent.execute = AsyncMock(side_effect=mock_execute)
        mock_agent.cleanup = AsyncMock(side_effect=mock_cleanup)
        
        self.mocks[f"agent_{name}"] = mock_agent
        return mock_agent
    
    # ========================================================================
    # External Service Mocks
    # ========================================================================
    
    def create_mock_supabase_client(self) -> MagicMock:
        """Create a mock Supabase client."""
        mock_client = MagicMock()
        
        # Mock user operations
        mock_client.get_user.return_value = {
            "id": f"user-{uuid.uuid4()}",
            "email": "<EMAIL>",
            "role": "attorney"
        }
        
        # Mock tenant operations
        mock_client.get_tenant.return_value = {
            "id": f"tenant-{uuid.uuid4()}",
            "name": "Test Tenant",
            "settings": {}
        }
        
        # Mock query operations
        mock_client.query.return_value = []
        mock_client.insert.return_value = {"id": f"record-{uuid.uuid4()}"}
        mock_client.update.return_value = {"id": f"record-{uuid.uuid4()}"}
        mock_client.delete.return_value = True
        
        self.mocks["supabase_client"] = mock_client
        return mock_client
    
    def create_mock_pinecone_client(self) -> MagicMock:
        """Create a mock Pinecone client."""
        mock_client = MagicMock()
        mock_index = MagicMock()
        
        # Mock query results
        mock_index.query.return_value = {
            "matches": [
                {
                    "id": f"doc-{i}",
                    "score": 0.9 - (i * 0.1),
                    "metadata": {
                        "text": f"Test document {i} content",
                        "title": f"Document {i}",
                        "type": "legal_document"
                    }
                }
                for i in range(1, 4)
            ]
        }
        
        # Mock upsert operations
        mock_index.upsert.return_value = {"upserted_count": 1}
        
        # Mock delete operations
        mock_index.delete.return_value = {"deleted_count": 1}
        
        mock_client.Index.return_value = mock_index
        self.mocks["pinecone_client"] = mock_client
        return mock_client
    
    def create_mock_openai_client(self) -> MagicMock:
        """Create a mock OpenAI client."""
        mock_client = MagicMock()
        
        # Mock chat completion
        mock_completion = MagicMock()
        mock_completion.choices = [
            MagicMock(
                message=MagicMock(
                    content="This is a test response from the AI assistant.",
                    role="assistant"
                ),
                finish_reason="stop"
            )
        ]
        mock_completion.usage = MagicMock(
            prompt_tokens=50,
            completion_tokens=25,
            total_tokens=75
        )
        
        mock_client.chat.completions.create.return_value = mock_completion
        
        # Mock embeddings
        mock_embedding = MagicMock()
        mock_embedding.data = [
            MagicMock(embedding=[0.1] * 1536)
        ]
        mock_client.embeddings.create.return_value = mock_embedding
        
        self.mocks["openai_client"] = mock_client
        return mock_client
    
    def create_mock_voyage_client(self) -> MagicMock:
        """Create a mock Voyage embeddings client."""
        mock_client = MagicMock()
        
        # Mock embedding methods
        mock_client.embed_query.return_value = [0.1] * 1536
        mock_client.embed_documents.return_value = [[0.1] * 1536] * 3
        
        self.mocks["voyage_client"] = mock_client
        return mock_client
    
    # ========================================================================
    # Tool and Executor Mocks
    # ========================================================================
    
    def create_mock_tool_executor(
        self,
        tool_responses: Optional[Dict[str, Any]] = None
    ) -> AsyncMock:
        """
        Create a mock tool executor.
        
        Args:
            tool_responses: Mapping of tool names to responses
            
        Returns:
            Mock tool executor
        """
        mock_executor = AsyncMock()
        
        async def execute_tool(tool_name: str, tool_args: Dict[str, Any], tenant_id: str):
            if tool_responses and tool_name in tool_responses:
                return tool_responses[tool_name]
            
            # Default response
            return {
                "result": "success",
                "tool_name": tool_name,
                "args": tool_args,
                "tenant_id": tenant_id,
                "execution_time": 0.1
            }
        
        mock_executor.execute_tool.side_effect = execute_tool
        
        self.mocks["tool_executor"] = mock_executor
        return mock_executor
    
    # ========================================================================
    # Database Mocks
    # ========================================================================
    
    def create_mock_database_session(self) -> MagicMock:
        """Create a mock database session."""
        mock_session = MagicMock()
        
        # Mock query operations
        mock_query = MagicMock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.first.return_value = None
        mock_query.all.return_value = []
        mock_query.count.return_value = 0
        
        mock_session.query.return_value = mock_query
        
        # Mock transaction operations
        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.rollback = MagicMock()
        mock_session.close = MagicMock()
        mock_session.flush = MagicMock()
        
        self.mocks["database_session"] = mock_session
        return mock_session
    
    # ========================================================================
    # State and Configuration Mocks
    # ========================================================================
    
    def create_mock_state_manager(self) -> MagicMock:
        """Create a mock state manager."""
        mock_manager = MagicMock()
        
        # Mock state operations
        mock_manager.save_state.return_value = True
        mock_manager.load_state.return_value = None
        mock_manager.delete_state.return_value = True
        mock_manager.list_states.return_value = []
        
        self.mocks["state_manager"] = mock_manager
        return mock_manager
    
    def create_mock_config_manager(self) -> MagicMock:
        """Create a mock configuration manager."""
        mock_manager = MagicMock()
        
        # Mock configuration operations
        mock_manager.get_config.return_value = {}
        mock_manager.set_config.return_value = True
        mock_manager.delete_config.return_value = True
        mock_manager.list_configs.return_value = []
        
        self.mocks["config_manager"] = mock_manager
        return mock_manager
    
    # ========================================================================
    # Error Simulation
    # ========================================================================
    
    def create_error_mock(
        self,
        error_type: type,
        error_message: str = "Test error",
        call_count: int = 1
    ) -> MagicMock:
        """
        Create a mock that raises an error after specified calls.
        
        Args:
            error_type: Type of error to raise
            error_message: Error message
            call_count: Number of calls before raising error
            
        Returns:
            Mock that raises error
        """
        mock = MagicMock()
        
        def side_effect(*args, **kwargs):
            if mock.call_count >= call_count:
                raise error_type(error_message)
            return {"result": "success"}
        
        mock.side_effect = side_effect
        return mock
    
    # ========================================================================
    # Performance Testing Mocks
    # ========================================================================
    
    def create_slow_mock(self, delay: float = 1.0) -> AsyncMock:
        """
        Create a mock that simulates slow operations.
        
        Args:
            delay: Delay in seconds
            
        Returns:
            Slow async mock
        """
        async def slow_operation(*args, **kwargs):
            await asyncio.sleep(delay)
            return {"result": "success", "delay": delay}
        
        mock = AsyncMock(side_effect=slow_operation)
        self.mocks[f"slow_mock_{delay}"] = mock
        return mock
