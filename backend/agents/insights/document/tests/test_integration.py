"""
Integration Tests for Document Agent

This module contains integration tests that verify the complete
document generation workflow works end-to-end.
"""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime

from backend.agents.shared.core.state import Agent<PERSON>xecution<PERSON>ontext, AgentStatus
from backend.agents.insights.document.agent import DocumentAgent
from backend.agents.insights.document.state import (
    DocumentState, DocumentRequest, DocumentType, PracticeArea,
    TemplateInfo, TemplateSource, DocumentOutput
)


@pytest.fixture
def mock_supabase_response():
    """Mock Supabase response data."""
    return {
        "data": [
            {
                "id": "template-123",
                "name": "Demand Letter Template",
                "content": "Dear {{defendant_name}},\n\nThis letter demands payment of ${{total_damages}} for injuries sustained by {{client_name}} on {{incident_date}}.\n\nSincerely,\n{{attorney_name}}",
                "practice_area": "personal_injury",
                "document_type": "demand_letter",
                "state": "Texas",
                "is_active": True,
                "template_engine": "simple",
                "variables": {
                    "required": ["client_name", "defendant_name", "total_damages", "incident_date", "attorney_name"],
                    "optional": []
                },
                "allows_conditional_blocks": False,
                "allows_loops": False,
                "category": "Pre-Litigation",
                "subcategory": None,
                "version": 1,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        ]
    }


class TestDocumentAgentIntegration:
    """Integration tests for Document Agent."""
    
    @patch('backend.agents.insights.document.database.template_repository.create_client')
    async def test_complete_document_generation_workflow(self, mock_create_client, mock_supabase_response):
        """Test complete document generation from request to output."""
        # Mock Supabase client
        mock_client = AsyncMock()
        mock_client.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value = mock_supabase_response
        mock_create_client.return_value = mock_client
        
        # Create execution context
        execution_context = AgentExecutionContext(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="document"
        )
        
        # Create document request
        document_request = DocumentRequest(
            document_type=DocumentType.DEMAND_LETTER,
            practice_area=PracticeArea.PERSONAL_INJURY,
            matter_id="matter-789",
            variables={
                "client_name": "John Doe",
                "defendant_name": "ABC Insurance Company",
                "total_damages": "75000",
                "incident_date": "January 15, 2024",
                "attorney_name": "Jane Attorney, Esq."
            }
        )
        
        # Create initial state
        initial_state = DocumentState(
            execution_context=execution_context,
            document_request=document_request,
            status=AgentStatus.PENDING
        )
        
        # Initialize and run agent
        agent = DocumentAgent()
        result = await agent.run(initial_state)
        
        # Verify results
        assert result.status == AgentStatus.COMPLETED
        assert result.generated_document is not None
        assert result.generated_document.content is not None
        assert "John Doe" in result.generated_document.content
        assert "ABC Insurance Company" in result.generated_document.content
        assert "$75000" in result.generated_document.content
        assert "January 15, 2024" in result.generated_document.content
        assert "Jane Attorney, Esq." in result.generated_document.content
        
        # Verify template was selected
        assert result.selected_template is not None
        assert result.selected_template.source == TemplateSource.GLOBAL
        
        # Verify variables were collected
        assert result.template_variables is not None
        assert "client_name" in result.template_variables
        
        # Verify workflow completion flags
        assert result.template_selection_complete is True
        assert result.variable_collection_complete is True
        assert result.document_generation_complete is True
    
    @patch('backend.agents.insights.document.database.template_repository.create_client')
    async def test_tenant_template_priority(self, mock_create_client):
        """Test that tenant templates have priority over global templates."""
        # Mock tenant template response
        tenant_response = {
            "data": [
                {
                    "id": "tenant-template-456",
                    "title": "Custom Demand Letter",
                    "content": "Custom template for {{client_name}} vs {{defendant_name}}. Amount: ${{total_damages}}.",
                    "category": "demand_letter",
                    "tenant_id": "test-tenant-123",
                    "is_active": True,
                    "variables": {
                        "required": ["client_name", "defendant_name", "total_damages"]
                    },
                    "metadata": {},
                    "version": 1
                }
            ]
        }
        
        # Mock global template response (should not be used)
        global_response = {"data": []}
        
        # Mock Supabase client
        mock_client = AsyncMock()
        
        # Mock tenant template query (first call)
        mock_client.table.return_value.select.return_value.eq.return_value.eq.return_value.execute.return_value = tenant_response
        
        mock_create_client.return_value = mock_client
        
        # Create execution context
        execution_context = AgentExecutionContext(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="document"
        )
        
        # Create document request
        document_request = DocumentRequest(
            document_type=DocumentType.DEMAND_LETTER,
            practice_area=PracticeArea.PERSONAL_INJURY,
            variables={
                "client_name": "Jane Smith",
                "defendant_name": "XYZ Corp",
                "total_damages": "50000"
            }
        )
        
        # Create initial state
        initial_state = DocumentState(
            execution_context=execution_context,
            document_request=document_request,
            status=AgentStatus.PENDING
        )
        
        # Initialize and run agent
        agent = DocumentAgent()
        result = await agent.run(initial_state)
        
        # Verify tenant template was used
        assert result.status == AgentStatus.COMPLETED
        assert result.selected_template is not None
        assert result.selected_template.source == TemplateSource.TENANT
        assert result.selected_template.id == "tenant-template-456"
        assert "Custom template" in result.generated_document.content
    
    @patch('backend.agents.insights.document.database.template_repository.create_client')
    async def test_missing_template_handling(self, mock_create_client):
        """Test handling when no template is found."""
        # Mock empty responses
        empty_response = {"data": []}
        
        # Mock Supabase client
        mock_client = AsyncMock()
        mock_client.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value = empty_response
        mock_create_client.return_value = mock_client
        
        # Create execution context
        execution_context = AgentExecutionContext(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="document"
        )
        
        # Create document request for unsupported type
        document_request = DocumentRequest(
            document_type=DocumentType.OTHER,
            practice_area=PracticeArea.CRIMINAL_DEFENSE,
            variables={"client_name": "John Doe"}
        )
        
        # Create initial state
        initial_state = DocumentState(
            execution_context=execution_context,
            document_request=document_request,
            status=AgentStatus.PENDING
        )
        
        # Initialize and run agent
        agent = DocumentAgent()
        result = await agent.run(initial_state)
        
        # Verify error handling
        assert result.status == AgentStatus.ERROR
        assert len(result.errors) > 0
        assert "No template found" in result.errors[0]
        assert result.template_selection_complete is False
    
    @patch('backend.agents.insights.document.database.template_repository.create_client')
    async def test_variable_validation_failure(self, mock_create_client, mock_supabase_response):
        """Test handling of variable validation failures."""
        # Mock Supabase client
        mock_client = AsyncMock()
        mock_client.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value = mock_supabase_response
        mock_create_client.return_value = mock_client
        
        # Create execution context
        execution_context = AgentExecutionContext(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="document"
        )
        
        # Create document request with missing required variables
        document_request = DocumentRequest(
            document_type=DocumentType.DEMAND_LETTER,
            practice_area=PracticeArea.PERSONAL_INJURY,
            variables={
                "client_name": "John Doe",
                # Missing required variables: defendant_name, total_damages, etc.
            }
        )
        
        # Create initial state
        initial_state = DocumentState(
            execution_context=execution_context,
            document_request=document_request,
            status=AgentStatus.PENDING
        )
        
        # Initialize and run agent
        agent = DocumentAgent()
        result = await agent.run(initial_state)
        
        # Verify validation error handling
        assert result.status == AgentStatus.ERROR
        assert len(result.validation_errors) > 0
        assert any("missing" in error.lower() for error in result.validation_errors)
        assert result.variable_collection_complete is False
    
    async def test_convenience_method(self):
        """Test the convenience method for document generation."""
        # Create execution context
        execution_context = AgentExecutionContext(
            tenant_id="test-tenant-123",
            user_id="test-user-456",
            agent_type="document"
        )
        
        # Create document request
        document_request = DocumentRequest(
            document_type=DocumentType.DEMAND_LETTER,
            practice_area=PracticeArea.PERSONAL_INJURY,
            variables={"client_name": "John Doe"}
        )
        
        # Mock the run method to avoid actual execution
        agent = DocumentAgent()
        
        with patch.object(agent, 'run') as mock_run:
            mock_result = DocumentState(
                execution_context=execution_context,
                document_request=document_request,
                status=AgentStatus.COMPLETED
            )
            mock_run.return_value = mock_result
            
            result = await agent.generate_document_from_request(
                document_request,
                execution_context
            )
            
            assert result == mock_result
            mock_run.assert_called_once()
    
    @patch('backend.agents.insights.document.template_manager.TemplateManager')
    async def test_template_preview(self, mock_manager_class):
        """Test template preview functionality."""
        # Mock template manager
        mock_manager = AsyncMock()
        mock_manager.preview_template.return_value = (
            "Preview: Dear John Doe, this is a test template.",
            []
        )
        mock_manager_class.return_value = mock_manager
        
        agent = DocumentAgent()
        
        result = await agent.preview_template(
            template_id="template-123",
            variables={"client_name": "John Doe"},
            tenant_id="tenant-123"
        )
        
        assert result["success"] is True
        assert "John Doe" in result["content"]
        assert len(result["errors"]) == 0
