"""
Document Agent Module

This module implements a comprehensive document generation agent that creates legal documents
for Personal Injury, Family Law, and Criminal Defense practice areas with dual-template
architecture supporting both global templates and tenant-specific templates.

Key Features:
- Dual-template architecture (global + tenant-specific)
- Template priority handling (tenant > global > dynamic)
- Multi-practice area support
- Advanced template engine with conditional blocks and loops
- Tenant isolation and security
- Document generation and validation

Components:
- TemplateManager: Manages template selection and priority
- TemplateRepository: Database access for templates
- TemplateEngine: Template processing and rendering
- DocumentAgent: Main agent orchestration
- DocumentState: State management for document generation

Usage:
    from backend.agents.insights.document import DocumentAgent
    
    agent = DocumentAgent()
    result = await agent.generate_document(state, config)
"""

from .agent import DocumentAgent
from .state import DocumentState
from .template_manager import TemplateManager
from .template_engine import TemplateEngine

__all__ = [
    "DocumentAgent",
    "DocumentState", 
    "TemplateManager",
    "TemplateEngine"
]
