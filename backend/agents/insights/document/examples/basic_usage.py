"""
Basic Usage Examples for Document Agent

This script demonstrates how to use the Document Agent for generating
legal documents with the dual-template architecture.

Run this script to see examples of:
- Basic document generation
- Template selection
- Variable validation
- Error handling
"""

import asyncio
import os
from datetime import datetime

# Add the backend directory to the path for imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

from backend.agents.insights.document import DocumentAgent
from backend.agents.insights.document.state import (
    DocumentRequest, DocumentType, PracticeArea
)
from backend.agents.shared.core.state import AgentExecutionContext


async def example_demand_letter():
    """Example: Generate a demand letter for personal injury case."""
    print("=== Demand Letter Generation Example ===")
    
    # Create execution context
    execution_context = AgentExecutionContext(
        tenant_id="example-tenant-123",
        user_id="example-user-456",
        agent_type="document"
    )
    
    # Create document request
    document_request = DocumentRequest(
        document_type=DocumentType.DEMAND_LETTER,
        practice_area=PracticeArea.PERSONAL_INJURY,
        matter_id="matter-789",
        client_id="client-101",
        variables={
            "client_name": "John Doe",
            "client_address": "123 Main St, Dallas, TX 75201",
            "defendant_name": "ABC Insurance Company",
            "defendant_address": "456 Corporate Blvd, Dallas, TX 75202",
            "incident_date": "January 15, 2024",
            "incident_description": "Motor vehicle accident at the intersection of Main St and Oak Ave",
            "total_damages": "75000",
            "medical_expenses": "25000",
            "lost_wages": "15000",
            "pain_suffering": "35000",
            "attorney_name": "Jane Attorney, Esq.",
            "law_firm_name": "Example Law Firm",
            "claim_number": "CLM-2024-001"
        },
        metadata={
            "urgency": "high",
            "deadline": "2024-03-01"
        }
    )
    
    # Generate document
    try:
        agent = DocumentAgent()
        result = await agent.generate_document_from_request(
            document_request,
            execution_context
        )
        
        if result.generated_document:
            print(f"✅ Document generated successfully!")
            print(f"Title: {result.generated_document.title}")
            print(f"Template used: {result.generated_document.template_used.name}")
            print(f"Template source: {result.generated_document.template_used.source}")
            print(f"Quality score: {result.quality_score}")
            print("\n--- Document Content ---")
            print(result.generated_document.content[:500] + "..." if len(result.generated_document.content) > 500 else result.generated_document.content)
        else:
            print("❌ Document generation failed")
            if result.errors:
                print("Errors:")
                for error in result.errors:
                    print(f"  - {error}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*50 + "\n")


async def example_template_preview():
    """Example: Preview a template with variables."""
    print("=== Template Preview Example ===")
    
    try:
        agent = DocumentAgent()
        
        # Preview with sample variables
        preview_result = await agent.preview_template(
            template_id="sample-template-123",
            variables={
                "client_name": "Jane Smith",
                "defendant_name": "XYZ Corporation",
                "total_damages": "100000"
            },
            tenant_id="example-tenant-123"
        )
        
        if preview_result["success"]:
            print("✅ Template preview generated successfully!")
            print("\n--- Preview Content ---")
            print(preview_result["content"][:300] + "..." if len(preview_result["content"]) > 300 else preview_result["content"])
        else:
            print("❌ Template preview failed")
            if preview_result["errors"]:
                print("Errors:")
                for error in preview_result["errors"]:
                    print(f"  - {error}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*50 + "\n")


async def example_variable_validation():
    """Example: Demonstrate variable validation."""
    print("=== Variable Validation Example ===")
    
    # Create execution context
    execution_context = AgentExecutionContext(
        tenant_id="example-tenant-123",
        user_id="example-user-456",
        agent_type="document"
    )
    
    # Create document request with missing variables
    document_request = DocumentRequest(
        document_type=DocumentType.DEMAND_LETTER,
        practice_area=PracticeArea.PERSONAL_INJURY,
        variables={
            "client_name": "John Doe",
            # Missing required variables like defendant_name, total_damages, etc.
        }
    )
    
    try:
        agent = DocumentAgent()
        result = await agent.generate_document_from_request(
            document_request,
            execution_context
        )
        
        if result.validation_errors:
            print("❌ Variable validation failed (as expected)")
            print("Validation errors:")
            for error in result.validation_errors:
                print(f"  - {error}")
        else:
            print("✅ All variables validated successfully")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*50 + "\n")


async def example_multiple_practice_areas():
    """Example: Generate documents for different practice areas."""
    print("=== Multiple Practice Areas Example ===")
    
    # Create execution context
    execution_context = AgentExecutionContext(
        tenant_id="example-tenant-123",
        user_id="example-user-456",
        agent_type="document"
    )
    
    # Test different practice areas and document types
    test_cases = [
        {
            "name": "Personal Injury - Settlement Agreement",
            "document_type": DocumentType.SETTLEMENT_AGREEMENT,
            "practice_area": PracticeArea.PERSONAL_INJURY,
            "variables": {
                "client_name": "Alice Johnson",
                "defendant_name": "DEF Insurance",
                "settlement_amount": "125000"
            }
        },
        {
            "name": "Family Law - Client Communication",
            "document_type": DocumentType.CLIENT_COMMUNICATION,
            "practice_area": PracticeArea.FAMILY_LAW,
            "variables": {
                "client_name": "Bob Wilson",
                "case_type": "Divorce",
                "next_hearing_date": "March 15, 2024"
            }
        },
        {
            "name": "Criminal Defense - Motion",
            "document_type": DocumentType.MOTION,
            "practice_area": PracticeArea.CRIMINAL_DEFENSE,
            "variables": {
                "client_name": "Charlie Brown",
                "motion_type": "Motion to Suppress",
                "court_name": "District Court of Dallas County"
            }
        }
    ]
    
    agent = DocumentAgent()
    
    for test_case in test_cases:
        print(f"Testing: {test_case['name']}")
        
        document_request = DocumentRequest(
            document_type=test_case["document_type"],
            practice_area=test_case["practice_area"],
            variables=test_case["variables"]
        )
        
        try:
            result = await agent.generate_document_from_request(
                document_request,
                execution_context
            )
            
            if result.generated_document:
                print(f"  ✅ Generated: {result.generated_document.title}")
                print(f"  Template: {result.generated_document.template_used.source}")
            else:
                print(f"  ❌ Failed to generate document")
                if result.errors:
                    print(f"  Errors: {', '.join(result.errors[:2])}")
        
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        print()
    
    print("="*50 + "\n")


async def main():
    """Run all examples."""
    print("Document Agent Usage Examples")
    print("=" * 50)
    print()
    
    # Note: These examples will fail without proper database setup
    # They are meant to demonstrate the API usage patterns
    print("⚠️  Note: These examples require proper database setup with Supabase.")
    print("⚠️  They demonstrate API usage patterns but may fail without templates.")
    print()
    
    await example_demand_letter()
    await example_template_preview()
    await example_variable_validation()
    await example_multiple_practice_areas()
    
    print("Examples completed!")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
