"""
Database Module for Document Agent

This module provides database access patterns for the Document Agent,
including repositories for both global and tenant-specific templates.

Components:
- TemplateRepository: Unified access to both template types
- Database utilities and connection management
- Query builders for complex template searches

Usage:
    from backend.agents.insights.document.database import TemplateRepository
    
    repo = TemplateRepository(tenant_id="tenant-123")
    templates = await repo.get_templates_by_practice_area("personal_injury")
"""

from .template_repository import TemplateRepository

__all__ = ["TemplateRepository"]
