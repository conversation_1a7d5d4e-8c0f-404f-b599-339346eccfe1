# Document Agent

The Document Agent is a comprehensive document generation system that creates legal documents for Personal Injury, Family Law, and Criminal Defense practice areas using a dual-template architecture.

**Location**: `backend/agents/insights/document/` - This is the complete, production-ready implementation.

## Overview

The Document Agent supports both global templates (stored in `public.legal_templates`) and tenant-specific templates (stored in `tenants.authored_document_templates`) with intelligent priority handling and advanced template processing capabilities.

## Architecture

### Key Components

1. **DocumentAgent** - Main orchestrator using LangGraph workflow
2. **TemplateManager** - High-level template operations coordinator
3. **TemplateRepository** - Database access for both template types
4. **TemplateEngine** - Template processing and rendering
5. **DocumentState** - Enhanced state management for document operations

### Workflow

```mermaid
graph TD
    A[Document Request] --> B[Select Template]
    B --> C{Template Found?}
    C -->|Yes| D[Collect Variables]
    C -->|No| E[Error: No Template]
    D --> F{Variables Valid?}
    F -->|Yes| G[Generate Document]
    F -->|No| H[Error: Invalid Variables]
    G --> I[Validate Output]
    I --> J[Complete]
```

## Template Architecture

### Dual-Template System

The agent supports two types of templates with priority handling:

1. **Tenant-Specific Templates** (Highest Priority)
   - Table: `tenants.authored_document_templates`
   - Content Format: TEXT
   - Template Engine: Simple substitution
   - Tenant-isolated with RLS

2. **Global Templates** (Fallback)
   - Table: `public.legal_templates`
   - Content Format: JSONB
   - Template Engine: Jinja2 or simple
   - Advanced features: conditional blocks, loops

3. **Dynamic Generation** (Last Resort)
   - AI-generated templates when no template exists

### Template Selection Priority

```
Tenant Template → Global Template → Dynamic Generation
```

## Supported Practice Areas

- **Personal Injury**: Demand letters, settlement agreements, court filings
- **Family Law**: Divorce documents, custody agreements, support orders
- **Criminal Defense**: Motions, plea agreements, discovery requests

## Document Types

- Demand Letter
- Settlement Agreement
- Court Filing
- Client Communication
- Authorization Form
- Discovery Request
- Motion
- Complaint
- Contract
- Other

## Usage

### Basic Document Generation

```python
from backend.agents.insights.document import DocumentAgent
from backend.agents.insights.document.state import DocumentRequest, DocumentType, PracticeArea
from backend.agents.shared.core.state import AgentExecutionContext

# Create execution context
context = AgentExecutionContext(
    tenant_id="tenant-123",
    user_id="user-456",
    agent_type="document"
)

# Create document request
request = DocumentRequest(
    document_type=DocumentType.DEMAND_LETTER,
    practice_area=PracticeArea.PERSONAL_INJURY,
    matter_id="matter-789",
    variables={
        "client_name": "John Doe",
        "incident_date": "2024-01-15",
        "defendant_name": "ABC Insurance",
        "total_damages": "50000"
    }
)

# Generate document
agent = DocumentAgent()
result = await agent.generate_document_from_request(request, context)

if result.generated_document:
    print(f"Generated: {result.generated_document.title}")
    print(result.generated_document.content)
```

### Template Preview

```python
# Preview a template
preview = await agent.preview_template(
    template_id="template-123",
    variables={"client_name": "Jane Smith"},
    tenant_id="tenant-123"
)

print(preview["content"])
```

### Template Management

```python
from backend.agents.insights.document.template_manager import TemplateManager

manager = TemplateManager(tenant_id="tenant-123")

# Get available templates
templates = await manager.get_available_templates(
    practice_area=PracticeArea.PERSONAL_INJURY,
    document_type=DocumentType.DEMAND_LETTER
)

# Validate variables
errors = await manager.validate_template_variables(
    template_id="template-123",
    variables={"client_name": "John Doe"}
)
```

## Configuration

### Environment Variables

```bash
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
SUPABASE_ANON_KEY=your_anon_key
```

### Agent Configuration

```python
config = {
    "max_execution_time": 300,  # 5 minutes
    "enable_caching": True,
    "template_cache_size": 100
}

agent = DocumentAgent(config=config)
```

## Template Variables

### System Variables (Auto-injected)

- `tenant_id` - Current tenant ID
- `user_id` - Current user ID
- `generation_date` - Document generation timestamp
- `agent_version` - Agent version

### Common Document Variables

- `client_name` - Client full name
- `client_address` - Client address
- `matter_id` - Associated matter ID
- `incident_date` - Date of incident
- `attorney_name` - Attorney name
- `law_firm_name` - Law firm name

## Error Handling

The agent provides comprehensive error handling:

- Template not found
- Missing required variables
- Template rendering errors
- Validation failures
- Database connection issues

Errors are collected and returned in the state for proper handling.

## Testing

```bash
# Run document agent tests
cd backend/agents
python -m pytest insights/document/tests/ -v

# Run specific test
python -m pytest insights/document/tests/test_agent.py::test_document_generation -v
```

## Performance

- Template caching for improved performance
- Async/await throughout for non-blocking operations
- Efficient database queries with proper indexing
- Memory-efficient template processing

## Security

- Tenant isolation with RLS policies
- Input validation and sanitization
- Secure template variable handling
- Audit logging for document generation

## Monitoring

The agent provides comprehensive logging and metrics:

- Template selection decisions
- Variable validation results
- Document generation performance
- Error tracking and reporting
