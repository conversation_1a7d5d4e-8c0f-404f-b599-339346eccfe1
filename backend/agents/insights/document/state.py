"""
Document Agent State Management

This module defines the state classes for the Document Agent, extending the base
LangGraph state with document-specific fields and validation.

Key Features:
- Document generation context and tracking
- Template selection and processing state
- Variable collection and validation
- Document output management
- Error handling for document operations

Usage:
    from backend.agents.insights.document.state import DocumentState
    
    state = DocumentState(
        execution_context=context,
        document_request=request,
        status=AgentStatus.PENDING
    )
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from backend.agents.shared.core.state import BaseLangGraphState, AgentStatus


class DocumentType(str, Enum):
    """Supported document types."""
    DEMAND_LETTER = "demand_letter"
    SETTLEMENT_AGREEMENT = "settlement_agreement"
    COURT_FILING = "court_filing"
    CLIENT_COMMUNICATION = "client_communication"
    AUTHORIZATION_FORM = "authorization_form"
    DISCOVERY_REQUEST = "discovery_request"
    MOTION = "motion"
    COMPLAINT = "complaint"
    CONTRACT = "contract"
    OTHER = "other"


class PracticeArea(str, Enum):
    """Supported practice areas."""
    PERSONAL_INJURY = "personal_injury"
    FAMILY_LAW = "family_law"
    CRIMINAL_DEFENSE = "criminal_defense"


class TemplateSource(str, Enum):
    """Template source types."""
    GLOBAL = "global"
    TENANT = "tenant"
    DYNAMIC = "dynamic"


class DocumentRequest(BaseModel):
    """Document generation request."""
    
    document_type: DocumentType = Field(..., description="Type of document to generate")
    practice_area: PracticeArea = Field(..., description="Practice area for the document")
    matter_id: Optional[str] = Field(None, description="Associated matter ID")
    client_id: Optional[str] = Field(None, description="Associated client ID")
    template_id: Optional[str] = Field(None, description="Specific template ID to use")
    variables: Dict[str, Any] = Field(default_factory=dict, description="Template variables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @field_validator('document_type')
    @classmethod
    def validate_document_type(cls, v):
        """Validate document type."""
        if isinstance(v, str):
            try:
                return DocumentType(v)
            except ValueError:
                raise ValueError(f"Invalid document type: {v}")
        return v
    
    @field_validator('practice_area')
    @classmethod
    def validate_practice_area(cls, v):
        """Validate practice area."""
        if isinstance(v, str):
            try:
                return PracticeArea(v)
            except ValueError:
                raise ValueError(f"Invalid practice area: {v}")
        return v


class TemplateInfo(BaseModel):
    """Template information."""
    
    id: str = Field(..., description="Template ID")
    name: str = Field(..., description="Template name")
    source: TemplateSource = Field(..., description="Template source")
    content: Union[str, Dict[str, Any]] = Field(..., description="Template content")
    variables: Dict[str, Any] = Field(default_factory=dict, description="Template variables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Template metadata")


class DocumentOutput(BaseModel):
    """Generated document output."""
    
    content: str = Field(..., description="Generated document content")
    title: str = Field(..., description="Document title")
    template_used: TemplateInfo = Field(..., description="Template information")
    variables_used: Dict[str, Any] = Field(default_factory=dict, description="Variables used")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Output metadata")
    format: str = Field(default="text", description="Document format")


class DocumentState(BaseLangGraphState):
    """
    Enhanced state for Document Agent operations.
    
    Extends the base LangGraph state with document-specific fields
    for template management, variable collection, and document generation.
    """
    
    # Document generation request
    document_request: Optional[DocumentRequest] = Field(None, description="Document generation request")
    
    # Template processing
    selected_template: Optional[TemplateInfo] = Field(None, description="Selected template")
    available_templates: List[TemplateInfo] = Field(default_factory=list, description="Available templates")
    template_variables: Dict[str, Any] = Field(default_factory=dict, description="Collected template variables")
    
    # Document output
    generated_document: Optional[DocumentOutput] = Field(None, description="Generated document")
    
    # Processing state
    variable_collection_complete: bool = Field(default=False, description="Variable collection status")
    template_selection_complete: bool = Field(default=False, description="Template selection status")
    document_generation_complete: bool = Field(default=False, description="Document generation status")
    
    # Validation and quality
    validation_errors: List[str] = Field(default_factory=list, description="Validation errors")
    quality_score: Optional[float] = Field(None, description="Document quality score")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True
        use_enum_values = True
