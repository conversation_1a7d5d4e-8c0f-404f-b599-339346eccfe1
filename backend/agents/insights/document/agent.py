"""
Document Agent

This module implements the main Document Agent class, which orchestrates
the document generation workflow using LangGraph.

Key Features:
- Document generation workflow orchestration
- Template selection and processing
- Variable collection and validation
- Document rendering and quality checks
- Error handling and recovery

Usage:
    from backend.agents.insights.document import DocumentAgent
    
    agent = DocumentAgent()
    result = await agent.run(state, config)
"""

import logging
from typing import Any, Dict, Optional
from langgraph.graph import StateGraph, END

from backend.agents.shared.core.base_agent import BaseAgent
from backend.agents.shared.core.state import AgentStatus, AgentExecutionContext
from .state import DocumentState, DocumentRequest
from .nodes import select_template, collect_variables, generate_document, validate_output

logger = logging.getLogger(__name__)


class DocumentAgent(BaseAgent):
    """
    Document generation agent using LangGraph workflow.
    
    Implements a comprehensive document generation workflow with template
    selection, variable collection, rendering, and validation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Document Agent.
        
        Args:
            config: Optional agent configuration
        """
        super().__init__(
            agent_type="document",
            agent_name="DocumentAgent",
            config=config or {}
        )
        
        # Build the workflow graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """
        Build the LangGraph workflow for document generation.
        
        Returns:
            Configured StateGraph
        """
        # Create the graph
        workflow = StateGraph(DocumentState)
        
        # Add nodes
        workflow.add_node("select_template", select_template)
        workflow.add_node("collect_variables", collect_variables)
        workflow.add_node("generate_document", generate_document)
        workflow.add_node("validate_output", validate_output)
        
        # Set entry point
        workflow.set_entry_point("select_template")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "select_template",
            self._should_continue_after_template_selection,
            {
                "continue": "collect_variables",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "collect_variables",
            self._should_continue_after_variable_collection,
            {
                "continue": "generate_document",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "generate_document",
            self._should_continue_after_generation,
            {
                "continue": "validate_output",
                "end": END
            }
        )
        
        workflow.add_edge("validate_output", END)
        
        return workflow.compile()
    
    async def initialize(
        self,
        state: DocumentState,
        config: Optional[Dict[str, Any]] = None
    ) -> DocumentState:
        """
        Initialize the document agent.
        
        Args:
            state: Current state
            config: Optional configuration
            
        Returns:
            Updated state
        """
        logger.info("Initializing Document Agent")
        
        # Validate execution context
        if not state.execution_context:
            raise ValueError("Missing execution context")
        
        # Set initial status
        state.status = AgentStatus.INITIALIZING
        
        # Validate document request
        if not state.document_request:
            logger.warning("No document request provided")
        
        logger.info("Document Agent initialized successfully")
        return state
    
    async def execute(
        self,
        state: DocumentState,
        config: Optional[Dict[str, Any]] = None
    ) -> DocumentState:
        """
        Execute the document generation workflow.
        
        Args:
            state: Current state
            config: Optional configuration
            
        Returns:
            Updated state with results
        """
        logger.info("Executing Document Agent workflow")
        
        try:
            # Set execution status
            state.status = AgentStatus.EXECUTING
            
            # Run the workflow
            result = await self.graph.ainvoke(state, config)
            
            # Update state with results
            for key, value in result.items():
                if hasattr(state, key):
                    setattr(state, key, value)
            
            # Set final status based on results
            if state.errors or state.validation_errors:
                state.status = AgentStatus.ERROR
            elif state.document_generation_complete:
                state.status = AgentStatus.COMPLETED
            else:
                state.status = AgentStatus.PARTIAL
            
            logger.info(f"Document Agent execution completed with status: {state.status}")
            return state
            
        except Exception as e:
            logger.error(f"Error in Document Agent execution: {e}")
            state.status = AgentStatus.ERROR
            state.errors.append(f"Agent execution failed: {str(e)}")
            return state
    
    async def cleanup(
        self,
        state: DocumentState,
        config: Optional[Dict[str, Any]] = None
    ) -> DocumentState:
        """
        Cleanup after document generation.
        
        Args:
            state: Current state
            config: Optional configuration
            
        Returns:
            Updated state
        """
        logger.info("Cleaning up Document Agent")
        
        # Log final results
        if state.generated_document:
            logger.info(f"Generated document: {state.generated_document.title}")
            logger.info(f"Quality score: {state.quality_score}")
        
        if state.errors:
            logger.warning(f"Errors encountered: {state.errors}")
        
        return state
    
    def _should_continue_after_template_selection(self, state: DocumentState) -> str:
        """Determine if workflow should continue after template selection."""
        if state.template_selection_complete and state.selected_template:
            return "continue"
        return "end"
    
    def _should_continue_after_variable_collection(self, state: DocumentState) -> str:
        """Determine if workflow should continue after variable collection."""
        if state.variable_collection_complete and not state.validation_errors:
            return "continue"
        return "end"
    
    def _should_continue_after_generation(self, state: DocumentState) -> str:
        """Determine if workflow should continue after document generation."""
        if state.document_generation_complete and state.generated_document:
            return "continue"
        return "end"
    
    async def generate_document_from_request(
        self,
        request: DocumentRequest,
        execution_context: AgentExecutionContext
    ) -> DocumentState:
        """
        Convenience method to generate a document from a request.
        
        Args:
            request: Document generation request
            execution_context: Execution context
            
        Returns:
            Final document state
        """
        # Create initial state
        state = DocumentState(
            execution_context=execution_context,
            document_request=request,
            status=AgentStatus.PENDING
        )
        
        # Run the agent
        return await self.run(state)
    
    async def preview_template(
        self,
        template_id: str,
        variables: Dict[str, Any],
        tenant_id: str
    ) -> Dict[str, Any]:
        """
        Preview a template with variables.
        
        Args:
            template_id: Template ID
            variables: Variables for preview
            tenant_id: Tenant ID
            
        Returns:
            Preview result
        """
        from .template_manager import TemplateManager
        
        template_manager = TemplateManager(tenant_id)
        content, errors = await template_manager.preview_template(template_id, variables)
        
        return {
            "content": content,
            "errors": errors,
            "success": len(errors) == 0
        }
