"""
Supervisor Agent Package

This package contains the Supervisor Agent implementation for the AiLex system.
The Supervisor Agent is responsible for:

1. Classifying user intent in ≤ 1 LLM call
2. Dispatching to the correct specialized agent
3. Maintaining tenant isolation
4. Failing gracefully with appropriate fallbacks

The implementation follows LangGraph's newest patterns, including the Command API
for routing and structured output parsing for robust JSON handling.
"""

from .agent import SupervisorAgent

__all__ = ["SupervisorAgent"]
