"""
Supervisor Agent Implementation

This module provides the SupervisorAgent class, which implements the supervisor agent
for the AiLex system following the unified architecture development guidelines.

The Supervisor Agent is responsible for:
1. Classifying user intent in ≤ 1 LLM call
2. Dispatching to the correct specialized agent
3. Maintaining tenant isolation
4. Failing gracefully with appropriate fallbacks

The implementation follows LangGraph's newest patterns, including the Command API
for routing and structured output parsing for robust JSON handling.
"""

import logging
from typing import Any, Dict, Optional

# Import from the LangGraph-compatible BaseAgent
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../'))

try:
    from src.pi_lawyer.agents.base_agent import BaseAgent
    from src.pi_lawyer.agents.config import AgentConfig
except ImportError:
    # Fallback to shared core if pi_lawyer agents not available
    from shared.core.base_agent import BaseAgent, AgentConfig

try:
    from langgraph.graph import StateGraph, END
    from langchain_core.runnables import RunnableConfig
except ImportError:
    # Fallback for environments without LangGraph
    StateGraph = None
    END = None
    RunnableConfig = None

from .state import SupervisorState
from .nodes import SupervisorNodes

# Configure logger
logger = logging.getLogger(__name__)


class SupervisorAgent(BaseAgent):
    """
    Supervisor Agent for the AiLex system.

    This agent is responsible for:
    1. Classifying user intent in ≤ 1 LLM call
    2. Dispatching to the correct specialized agent
    3. Maintaining tenant isolation
    4. Failing gracefully with appropriate fallbacks

    It serves as the entry point for the LangGraph system, routing requests
    to the appropriate specialized agent or queuing async jobs.
    """

    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the Supervisor Agent."""
        if config is None:
            config = AgentConfig(
                name="supervisor_agent",
                agent_type="insights",
                description="Legal assistant supervisor that routes to specialized agents",
                version="1.0.0"
            )

        super().__init__(config)
        self.nodes = SupervisorNodes()
        
    async def initialize(self, state: SupervisorState, config: Any = None) -> SupervisorState:
        """
        Initialize the agent with necessary setup.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Initializing Supervisor Agent")
        
        # Tenant validation
        tenant_id = self._get_tenant_id(config)
        if not tenant_id:
            raise ValueError("tenant_id is required")
        
        # Initialize state
        state.tenant_id = tenant_id
        state.user_id = self._get_user_id(config)
        state.thread_id = self._get_thread_id(config)
        state.initialized = True
        state.status = "initialized"
        
        logger.info(f"Supervisor Agent initialized for tenant: {tenant_id}")
        return state
    
    async def execute(self, state: SupervisorState, config: Any = None) -> SupervisorState:
        """
        Execute the main agent logic.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Executing Supervisor Agent")
        
        try:
            # Step 1: Classify intent
            if not state.classified:
                state = await self.nodes.classify_intent(state, config)
            
            # Step 2: Route request
            if state.classified and not state.routed:
                state = await self.nodes.route_request(state, config)
            
            # Update status
            if state.routed and not state.error:
                state.status = "completed"
            elif state.error:
                state.status = "failed"
            
        except Exception as e:
            logger.error(f"Supervisor Agent execution failed: {e}")
            state.error = str(e)
            state.status = "failed"
        
        return state
    
    async def cleanup(self, state: SupervisorState, config: Any = None) -> SupervisorState:
        """
        Cleanup resources and finalize state.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Cleaning up Supervisor Agent")
        
        # Cleanup logic here
        state.cleanup_completed = True
        
        # Log final state
        if state.selected_agent:
            logger.info(f"Request routed to: {state.selected_agent}")
        
        return state
    
    def create_graph(self):
        """
        Create the LangGraph workflow.

        Returns:
            Compiled StateGraph or fallback implementation
        """
        if StateGraph is None:
            # Fallback for environments without LangGraph
            logger.warning("LangGraph not available, using fallback implementation")
            return self._create_fallback_graph()

        workflow = StateGraph(SupervisorState)

        # Add nodes
        workflow.add_node("initialize", self.initialize)
        workflow.add_node("execute", self.execute)
        workflow.add_node("cleanup", self.cleanup)

        # Define edges
        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "execute")
        workflow.add_edge("execute", "cleanup")
        workflow.add_edge("cleanup", END)

        return workflow.compile()

    def _create_fallback_graph(self):
        """
        Create a fallback graph implementation.

        Returns:
            Simple callable that mimics LangGraph behavior
        """
        async def fallback_invoke(state, config=None):
            """Fallback invoke method."""
            # Convert dict to SupervisorState if needed
            if isinstance(state, dict):
                state = SupervisorState(**state)

            # Run the workflow manually
            state = await self.initialize(state, config or {})
            state = await self.execute(state, config or {})
            state = await self.cleanup(state, config or {})

            return state

        # Return an object that mimics LangGraph's interface
        class FallbackGraph:
            def __init__(self, invoke_func):
                self.ainvoke = invoke_func
                self.invoke = invoke_func  # For sync calls

        return FallbackGraph(fallback_invoke)
    
    def _get_tenant_id(self, config: Any) -> Optional[str]:
        """
        Extract tenant ID from config.

        Args:
            config: Runnable configuration

        Returns:
            Tenant ID if found
        """
        if not config:
            return None

        if isinstance(config, dict):
            return config.get('configurable', {}).get('tenant_id')

        configurable = getattr(config, 'configurable', {})
        return configurable.get('tenant_id')

    def _get_user_id(self, config: Any) -> Optional[str]:
        """
        Extract user ID from config.

        Args:
            config: Runnable configuration

        Returns:
            User ID if found
        """
        if not config:
            return None

        if isinstance(config, dict):
            return config.get('configurable', {}).get('user_id')

        configurable = getattr(config, 'configurable', {})
        return configurable.get('user_id')

    def _get_thread_id(self, config: Any) -> Optional[str]:
        """
        Extract thread ID from config.

        Args:
            config: Runnable configuration

        Returns:
            Thread ID if found
        """
        if not config:
            return None

        if isinstance(config, dict):
            return config.get('configurable', {}).get('thread_id')

        configurable = getattr(config, 'configurable', {})
        return configurable.get('thread_id')
