"""
Supervisor Agent State Models

This module defines the state models for the Supervisor Agent following the
unified architecture development guidelines.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

# Import from the correct state location
try:
    from shared.core.state import AiLexState as BaseLangGraphState
except ImportError:
    # Fallback if shared state not available
    from typing import List, Any
    from pydantic import BaseModel

    class BaseLangGraphState(BaseModel):
        """Fallback base state."""
        tenant_id: Optional[str] = None
        user_id: Optional[str] = None
        thread_id: Optional[str] = None
        messages: List[Any] = Field(default_factory=list)


class AgentType(str, Enum):
    """Available agent types for routing."""
    INTAKE = "intakeAgent"
    RESEARCH = "researchAgent"
    TASK_CRUD = "taskCrudAgent"
    CALENDAR_CRUD = "calendarCrudAgent"
    DOCUMENT_DRAFT = "documentDraftAgent"
    INSIGHT_SWARM = "insightSwarmAgent"


class Classification(BaseModel):
    """
    Classification result from the Supervisor Agent.
    
    This model defines the structure of the classification result,
    including the agent to route to, arguments to pass, and confidence.
    """
    agent: AgentType = Field(..., description="The agent to route to")
    args: Dict[str, Any] = Field(default_factory=dict, description="Arguments to pass to the agent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0.0-1.0)")
    
    @validator('confidence')
    def check_confidence(cls, v):
        """Ensure confidence is between 0 and 1."""
        if v < 0 or v > 1:
            return max(0.0, min(v, 1.0))
        return v


class SupervisorState(BaseLangGraphState):
    """State model for Supervisor Agent."""
    
    # Classification results
    classification: Optional[Classification] = Field(None, description="Intent classification result")
    selected_agent: Optional[str] = Field(None, description="Selected agent for routing")
    routing_args: Dict[str, Any] = Field(default_factory=dict, description="Arguments for routing")
    
    # Processing status
    status: str = Field("pending", description="Processing status")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    # Workflow tracking
    initialized: bool = Field(False, description="Whether agent is initialized")
    classified: bool = Field(False, description="Whether intent is classified")
    routed: bool = Field(False, description="Whether request is routed")
    cleanup_completed: bool = Field(False, description="Whether cleanup is completed")
    
    # Cache and performance
    cache_key: Optional[str] = Field(None, description="Cache key for classification")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    
    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow additional fields for flexibility
        validate_assignment = True


# Function calling schema for LLM
CLASSIFY_FN_SCHEMA = {
    "name": "classify_intent",
    "description": "Classify user intent and determine which agent should handle the request",
    "parameters": {
        "type": "object",
        "properties": {
            "agent": {
                "type": "string",
                "enum": [e.value for e in AgentType],
                "description": "The agent to route to"
            },
            "args": {
                "type": "object",
                "description": "Arguments to pass to the agent"
            },
            "confidence": {
                "type": "number",
                "minimum": 0,
                "maximum": 1,
                "description": "Confidence score (0.0-1.0)"
            }
        },
        "required": ["agent", "confidence"]
    }
}
