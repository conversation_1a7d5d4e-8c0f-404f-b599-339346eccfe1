"""
Tests for Supervisor Agent

This module contains unit tests for the Supervisor Agent implementation
following the unified architecture development guidelines.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock

from ..agent import SupervisorAgent
from ..state import SupervisorState, AgentType, Classification


@pytest.mark.asyncio
class TestSupervisorAgent:
    """Tests for Supervisor Agent."""
    
    @pytest.fixture
    def agent(self):
        """Create a supervisor agent instance."""
        return SupervisorAgent()
    
    @pytest.fixture
    def state(self):
        """Create a test state."""
        return SupervisorState(
            tenant_id="test-tenant",
            user_id="test-user",
            thread_id="test-thread",
            messages=[]
        )
    
    @pytest.fixture
    def config(self):
        """Create a test config."""
        return MagicMock(
            configurable={
                "tenant_id": "test-tenant",
                "user_id": "test-user",
                "thread_id": "test-thread"
            }
        )
    
    async def test_initialize(self, agent, state, config):
        """Test agent initialization."""
        result = await agent.initialize(state, config)
        
        assert result.initialized is True
        assert result.tenant_id == "test-tenant"
        assert result.user_id == "test-user"
        assert result.thread_id == "test-thread"
        assert result.status == "initialized"
    
    async def test_initialize_missing_tenant(self, agent, state):
        """Test initialization with missing tenant ID."""
        config = MagicMock(configurable={})
        
        with pytest.raises(ValueError, match="tenant_id is required"):
            await agent.initialize(state, config)
    
    async def test_execute_success(self, agent, state, config):
        """Test successful agent execution."""
        # Initialize first
        state.initialized = True
        state.tenant_id = "test-tenant"
        
        # Mock a human message
        human_message = MagicMock()
        human_message.type = "human"
        human_message.content = "Research statute of limitations"
        state.messages = [human_message]
        
        result = await agent.execute(state, config)
        
        assert result.classified is True
        assert result.routed is True
        assert result.status == "completed"
        assert result.classification is not None
        assert result.selected_agent is not None
    
    async def test_execute_no_messages(self, agent, state, config):
        """Test execution with no messages."""
        state.initialized = True
        state.tenant_id = "test-tenant"
        state.messages = []
        
        result = await agent.execute(state, config)
        
        # Should still complete but with default classification
        assert result.classified is True
        assert result.classification.agent == AgentType.INTAKE
        assert result.classification.confidence == 0.0
    
    async def test_cleanup(self, agent, state, config):
        """Test agent cleanup."""
        state.selected_agent = "researchAgent"
        
        result = await agent.cleanup(state, config)
        
        assert result.cleanup_completed is True
    
    async def test_create_graph(self, agent):
        """Test graph creation."""
        graph = agent.create_graph()
        
        assert graph is not None
        # Graph should be compiled and ready to use
    
    async def test_full_workflow(self, agent):
        """Test complete agent workflow."""
        graph = agent.create_graph()
        
        # Create a human message
        human_message = MagicMock()
        human_message.type = "human"
        human_message.content = "I need to research Texas personal injury law"
        
        initial_state = SupervisorState(
            tenant_id="test-tenant",
            user_id="test-user",
            thread_id="test-thread",
            messages=[human_message]
        )
        
        config = MagicMock(
            configurable={
                "tenant_id": "test-tenant",
                "user_id": "test-user",
                "thread_id": "test-thread"
            }
        )
        
        # This would normally run the full graph
        # For now, just test that the graph can be created
        assert graph is not None
