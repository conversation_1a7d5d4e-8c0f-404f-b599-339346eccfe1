# Supervisor Agent

## Overview

The Supervisor Agent is a critical component of the AiLex Unified Agent Architecture, responsible for classifying user intent and routing requests to the appropriate specialized agents. It serves as the entry point for the LangGraph system.

## Features

- **Intent Classification**: Analyzes user messages in ≤ 1 LLM call to determine intent
- **Agent Routing**: Dispatches requests to the correct specialized agent
- **Tenant Isolation**: Maintains strict tenant-based security
- **Graceful Fallbacks**: Handles errors and edge cases appropriately
- **Caching**: Implements intelligent caching for improved performance
- **Confidence Scoring**: Provides confidence scores for routing decisions

## Architecture

The Supervisor Agent follows the unified architecture pattern with:

- **BaseAgent Pattern**: Inherits from shared BaseAgent class
- **State Management**: Uses SupervisorState for type-safe state handling
- **Node-based Processing**: Implements classification and routing as separate nodes
- **LangGraph Integration**: Fully compatible with LangGraph workflows

## Usage

```python
from backend.agents.insights.supervisor.agent import SupervisorAgent

# Create supervisor agent
agent = SupervisorAgent()

# Create graph
graph = agent.create_graph()

# Execute with state and config
result = await graph.ainvoke(
    {
        "tenant_id": "tenant-123",
        "user_id": "user-456", 
        "thread_id": "thread-789",
        "messages": [{"type": "human", "content": "Research Texas personal injury law"}]
    },
    {
        "configurable": {
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "thread_id": "thread-789"
        }
    }
)
```

## Available Agents

The supervisor can route to the following agents:

- **intakeAgent**: For new client intake and case setup
- **researchAgent**: For legal research and finding relevant laws
- **taskCrudAgent**: For task management operations
- **calendarCrudAgent**: For calendar and scheduling operations
- **documentDraftAgent**: For document drafting and generation
- **insightSwarmAgent**: For complex analysis and insights

## Configuration

### Environment Variables

- `SUPERVISOR_CONFIDENCE_THRESHOLD`: Minimum confidence threshold for routing (default: 0.6)

### Caching

The agent implements intelligent caching with:
- **Cache Size**: 100 entries maximum
- **TTL**: 500 milliseconds
- **Key Generation**: Based on message content and tenant ID

## State Model

The SupervisorState includes:

```python
class SupervisorState(BaseLangGraphState):
    # Classification results
    classification: Optional[Classification]
    selected_agent: Optional[str]
    routing_args: Dict[str, Any]
    
    # Processing status
    status: str
    error: Optional[str]
    
    # Workflow tracking
    initialized: bool
    classified: bool
    routed: bool
    cleanup_completed: bool
```

## Testing

Run tests with:

```bash
pytest backend/agents/insights/supervisor/tests/
```

## Dependencies

- **LangGraph**: For workflow orchestration
- **Pydantic**: For state validation
- **LangChain**: For LLM integration
- **Shared Core**: For base agent functionality

## Performance

- **Response Time**: Optimized for ≤ 1 second classification
- **Memory Usage**: Efficient state management with cleanup
- **Scalability**: Stateless design for horizontal scaling
- **Caching**: Reduces redundant LLM calls

## Security

- **Tenant Isolation**: All operations are tenant-scoped
- **Input Validation**: Validates all user inputs
- **Error Handling**: Doesn't leak sensitive information
- **Authentication**: Verifies user permissions

## Migration Notes

This implementation replaces the previous supervisor agent located in `src/pi_lawyer/agents/insights/supervisor/`. Key improvements:

- **Unified Architecture**: Follows new development guidelines
- **Better State Management**: Type-safe state with Pydantic
- **Enhanced Error Handling**: More robust error handling and fallbacks
- **Improved Testing**: Comprehensive test coverage
- **Performance Optimizations**: Caching and efficient processing
