"""
Supervisor Agent Node Implementations

This module contains the implementation of the nodes for the Supervisor Agent.
Each node is a function that takes a state and returns an updated state or a command
to transition to another node.

The nodes implement the supervisor workflow, including:
- Intent classification and routing
- Agent selection and dispatch
- Error handling and fallbacks
"""

import hashlib
import json
import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict

try:
    from langchain_core.output_parsers.json import JsonOutputParser
    from langchain_core.runnables import RunnableConfig
    from langgraph.types import Command
except ImportError:
    # Fallback for environments without LangChain/LangGraph
    JsonOutputParser = None
    RunnableConfig = None
    Command = None

from pydantic import ValidationError

from .state import CLASSIFY_FN_SCHEMA, AgentType, Classification, SupervisorState

# Set up logging
logger = logging.getLogger(__name__)

# Define constants
INTERACTIVE_AGENTS = {
    "intakeAgent",
    "researchAgent", 
    "taskCrudAgent",
    "calendarCrudAgent"
}

ASYNC_AGENTS = {
    "documentDraftAgent",
    "insightSwarmAgent"
}

DEFAULT_AGENT = "intakeAgent"
CONFIDENCE_THRESHOLD = float(os.getenv("SUPERVISOR_CONFIDENCE_THRESHOLD", "0.6"))


class SupervisorNodes:
    """Node implementations for Supervisor Agent."""
    
    def __init__(self):
        """Initialize the supervisor nodes."""
        self.parser = JsonOutputParser() if JsonOutputParser else None
        self._cache = {}
        self._cache_max_size = 100
        self._cache_ttl_ms = 500  # milliseconds
        
    def _load_prompt_template(self) -> str:
        """
        Load the prompt template from the prompt.md file.
        
        Returns:
            The prompt template
        """
        import os
        
        # Get the directory of the current file
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Construct the path to the prompt.md file
        prompt_path = os.path.join(current_dir, "prompt.md")
        
        # Read the prompt template
        try:
            with open(prompt_path, "r") as f:
                prompt_template = f.read()
        except FileNotFoundError:
            # Fallback prompt if file not found
            prompt_template = """
You are a legal assistant supervisor that routes user requests to specialized agents.

User message: {{user_message}}
Tenant ID: {{tenant_id}}
Matter ID: {{matter_id or "None"}}
Locale: {{locale}}

Available agents:
- intakeAgent: For new client intake and case setup
- researchAgent: For legal research and finding relevant laws
- taskCrudAgent: For task management operations
- calendarCrudAgent: For calendar and scheduling operations
- documentDraftAgent: For document drafting and generation
- insightSwarmAgent: For complex analysis and insights

Classify the user's intent and select the most appropriate agent.
"""
        
        return prompt_template
    
    def _build_prompt(self, state: SupervisorState) -> str:
        """
        Build the prompt for the LLM.
        
        Args:
            state: Current state
            
        Returns:
            The prompt for the LLM
        """
        # Get the last user message
        last_message = None
        for message in reversed(state.messages):
            if hasattr(message, 'type') and message.type == "human":
                last_message = message
                break
            elif isinstance(message, dict) and message.get("type") == "human":
                last_message = message
                break
        
        if last_message is None:
            return "No user message found"
        
        user_message = getattr(last_message, 'content', '') or last_message.get('content', '')
        
        # Get tenant and matter information
        tenant_id = state.tenant_id or "unknown"
        matter_id = state.matter_id or "None"
        locale = getattr(state, 'locale', 'en-US')
        
        # Load and replace placeholders in the prompt template
        prompt_template = self._load_prompt_template()
        prompt = prompt_template.replace("{{user_message}}", str(user_message))
        prompt = prompt.replace("{{tenant_id}}", str(tenant_id))
        prompt = prompt.replace("{{matter_id or \"None\"}}", str(matter_id))
        prompt = prompt.replace("{{locale}}", str(locale))
        
        return prompt
    
    def _generate_cache_key(self, content: str, tenant_id: str) -> str:
        """
        Generate a cache key for the classification.
        
        Args:
            content: Message content
            tenant_id: Tenant ID
            
        Returns:
            Cache key
        """
        # Create a hash of the content and tenant_id
        content_hash = hashlib.md5(f"{content}:{tenant_id}".encode()).hexdigest()
        return f"classify:{content_hash}"
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """
        Check if a cache entry is still valid.
        
        Args:
            cache_entry: Cache entry to check
            
        Returns:
            True if valid, False otherwise
        """
        if "timestamp" not in cache_entry:
            return False
        
        now = datetime.now(timezone.utc).timestamp() * 1000  # milliseconds
        age = now - cache_entry["timestamp"]
        
        return age < self._cache_ttl_ms
    
    async def classify_intent(self, state: SupervisorState, config: Any = None) -> SupervisorState:
        """
        Classify the user's intent using structured output parsing.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state with classification result
        """
        logger.info("Classifying user intent")
        
        try:
            # Extract the last human message
            last_message = None
            for message in reversed(state.messages):
                if hasattr(message, 'type') and message.type == "human":
                    last_message = message
                    break
                elif isinstance(message, dict) and message.get("type") == "human":
                    last_message = message
                    break
            
            if not last_message:
                logger.warning("No human message found in state")
                state.classification = Classification(
                    agent=AgentType.INTAKE,
                    args={},
                    confidence=0.0
                )
                state.error = "No user message found"
                state.status = "failed"
                return state
            
            # Get message content
            content = getattr(last_message, 'content', '') or last_message.get('content', '')
            if isinstance(content, list):
                content = " ".join([str(c) for c in content])
            
            # Generate cache key
            cache_key = self._generate_cache_key(content, state.tenant_id or "")
            state.cache_key = cache_key
            
            # Check cache first
            if cache_key in self._cache and self._is_cache_valid(self._cache[cache_key]):
                logger.info("Using cached classification result")
                cached_result = self._cache[cache_key]["result"]
                state.classification = Classification(**cached_result)
                state.classified = True
                state.status = "completed"
                return state
            
            # Build prompt for classification
            prompt = self._build_prompt(state)
            
            # TODO: Implement LLM call for classification
            # For now, use simple keyword-based classification as fallback
            classification = self._fallback_classification(content)
            
            # Cache the result
            if len(self._cache) >= self._cache_max_size:
                # Remove oldest entry
                oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]["timestamp"])
                del self._cache[oldest_key]
            
            self._cache[cache_key] = {
                "result": classification.dict(),
                "timestamp": datetime.now(timezone.utc).timestamp() * 1000
            }
            
            state.classification = classification
            state.classified = True
            state.status = "completed"
            
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            state.error = str(e)
            state.status = "failed"
            # Fallback to default agent
            state.classification = Classification(
                agent=AgentType.INTAKE,
                args={},
                confidence=0.0
            )
        
        return state
    
    def _fallback_classification(self, content: str) -> Classification:
        """
        Fallback classification using simple keyword matching.
        
        Args:
            content: Message content
            
        Returns:
            Classification result
        """
        content_lower = content.lower()
        
        # Research keywords
        if any(keyword in content_lower for keyword in ["research", "find", "search", "law", "statute", "case"]):
            return Classification(agent=AgentType.RESEARCH, args={}, confidence=0.8)
        
        # Task keywords
        if any(keyword in content_lower for keyword in ["task", "todo", "remind", "deadline"]):
            return Classification(agent=AgentType.TASK_CRUD, args={}, confidence=0.8)
        
        # Calendar keywords
        if any(keyword in content_lower for keyword in ["calendar", "schedule", "appointment", "meeting"]):
            return Classification(agent=AgentType.CALENDAR_CRUD, args={}, confidence=0.8)
        
        # Document keywords
        if any(keyword in content_lower for keyword in ["document", "draft", "write", "letter", "contract"]):
            return Classification(agent=AgentType.DOCUMENT_DRAFT, args={}, confidence=0.8)
        
        # Default to intake
        return Classification(agent=AgentType.INTAKE, args={}, confidence=0.6)
    
    async def route_request(self, state: SupervisorState, config: Any = None) -> SupervisorState:
        """
        Route the request to the appropriate agent.
        
        Args:
            state: Current state with classification
            config: Runnable configuration
            
        Returns:
            Updated state with routing information
        """
        logger.info("Routing request to appropriate agent")
        
        try:
            if not state.classification:
                raise ValueError("No classification available for routing")
            
            agent = state.classification.agent.value
            confidence = state.classification.confidence
            
            # Check confidence threshold
            if confidence < CONFIDENCE_THRESHOLD:
                logger.warning(f"Low confidence ({confidence}) for agent {agent}, using default")
                agent = DEFAULT_AGENT
            
            state.selected_agent = agent
            state.routing_args = state.classification.args
            state.routed = True
            
            logger.info(f"Routed to agent: {agent} with confidence: {confidence}")
            
        except Exception as e:
            logger.error(f"Routing failed: {e}")
            state.error = str(e)
            state.status = "failed"
            state.selected_agent = DEFAULT_AGENT
            state.routing_args = {}
        
        return state
