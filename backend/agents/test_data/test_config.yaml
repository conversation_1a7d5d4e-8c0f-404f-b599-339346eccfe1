# Comprehensive Test Configuration for Agent System
# This file contains all configuration settings for different types of tests

# Performance Testing Configuration
performance:
  # Response time thresholds (in seconds)
  response_times:
    unit_tests: 0.1
    integration_tests: 2.0
    e2e_tests: 10.0
    api_calls: 5.0
    database_operations: 1.0
    
  # Memory usage thresholds (in MB)
  memory_limits:
    unit_tests: 50
    integration_tests: 200
    e2e_tests: 500
    single_agent: 100
    multi_agent_workflow: 300
    
  # Throughput requirements (operations per second)
  throughput:
    agent_execution: 10
    document_processing: 5
    search_operations: 20
    state_management: 50
    
  # Load testing configuration
  load_testing:
    default_concurrent_users: 10
    default_requests_per_user: 5
    ramp_up_time: 2.0
    test_duration: 30.0
    think_time_min: 0.1
    think_time_max: 0.5
    
  # Chaos engineering settings
  chaos_engineering:
    default_failure_rate: 0.1
    latency_injection:
      enabled: true
      min_delay: 0.1
      max_delay: 2.0
    memory_pressure:
      enabled: false
      pressure_level: 0.8
    network_partition:
      enabled: false
      partition_duration: 5.0

# Database Testing Configuration
database:
  # Test database settings
  test_db:
    url: "sqlite:///:memory:"
    pool_size: 5
    max_overflow: 10
    echo: false
    
  # Connection testing
  connection_timeout: 30.0
  query_timeout: 10.0
  transaction_timeout: 60.0
  
  # Test data management
  fixtures:
    auto_load: true
    cleanup_after_test: true
    isolation_level: "READ_COMMITTED"

# External API Testing Configuration
external_apis:
  # General API settings
  timeout: 30.0
  retry_attempts: 3
  retry_delay: 1.0
  circuit_breaker_threshold: 5
  
  # Service-specific configurations
  openai:
    mock_responses: true
    rate_limit: 60  # requests per minute
    model_fallbacks: ["gpt-4o", "gpt-3.5-turbo"]
    
  pinecone:
    mock_responses: true
    index_name: "test-index"
    dimension: 1536
    metric: "cosine"
    
  supabase:
    mock_responses: true
    project_url: "https://test.supabase.co"
    anon_key: "test-anon-key"
    
  voyage:
    mock_responses: true
    model: "voyage-large-2"
    batch_size: 128

# Agent Testing Configuration
agents:
  # Agent lifecycle testing
  lifecycle:
    initialization_timeout: 5.0
    execution_timeout: 30.0
    cleanup_timeout: 5.0
    
  # State management
  state:
    max_message_count: 1000
    max_metadata_size: 1048576  # 1MB
    serialization_format: "json"
    compression_enabled: false
    
  # Tool execution
  tools:
    execution_timeout: 10.0
    max_concurrent_tools: 5
    retry_failed_tools: true
    tool_isolation: true
    
  # Multi-agent workflows
  workflows:
    max_agents_per_workflow: 10
    workflow_timeout: 300.0  # 5 minutes
    error_propagation: "stop_on_critical"
    recovery_strategy: "retry_with_backoff"

# Test Data Configuration
test_data:
  # Data generation settings
  generation:
    seed: 42
    user_count: 100
    case_count: 50
    document_count: 200
    
  # Realistic data settings
  realistic_data:
    use_real_names: false
    use_real_addresses: false
    anonymize_sensitive_data: true
    
  # Test scenarios
  scenarios:
    load_from_file: true
    scenario_file: "realistic_scenarios.json"
    custom_scenarios_enabled: true

# Logging and Monitoring Configuration
logging:
  # Log levels for different components
  levels:
    root: "INFO"
    agents: "DEBUG"
    tests: "DEBUG"
    performance: "INFO"
    database: "WARNING"
    
  # Log formatting
  format: "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  
  # Log output
  console_output: true
  file_output: true
  log_file: "test_execution.log"
  
  # Performance logging
  performance_logging:
    enabled: true
    log_slow_operations: true
    slow_operation_threshold: 1.0

# Security Testing Configuration
security:
  # Input validation testing
  input_validation:
    test_sql_injection: true
    test_xss_attacks: true
    test_command_injection: true
    test_path_traversal: true
    
  # Authentication testing
  authentication:
    test_invalid_tokens: true
    test_expired_tokens: true
    test_privilege_escalation: true
    
  # Data protection
  data_protection:
    test_data_encryption: true
    test_pii_handling: true
    test_tenant_isolation: true

# Error Handling Configuration
error_handling:
  # Error simulation
  simulation:
    enabled: true
    error_injection_rate: 0.05
    error_types: ["timeout", "connection", "validation", "permission"]
    
  # Recovery testing
  recovery:
    test_retry_mechanisms: true
    test_circuit_breakers: true
    test_fallback_strategies: true
    test_graceful_degradation: true
    
  # Error reporting
  reporting:
    capture_stack_traces: true
    log_error_context: true
    aggregate_error_metrics: true

# CI/CD Integration Configuration
ci_cd:
  # Test execution settings
  execution:
    parallel_execution: true
    max_parallel_workers: 4
    test_timeout: 1800  # 30 minutes
    
  # Coverage requirements
  coverage:
    minimum_line_coverage: 90
    minimum_branch_coverage: 85
    fail_on_coverage_decrease: true
    
  # Quality gates
  quality_gates:
    max_test_failures: 0
    max_flaky_tests: 2
    max_performance_regressions: 1
    
  # Reporting
  reporting:
    generate_html_report: true
    generate_xml_report: true
    generate_json_report: true
    upload_to_codecov: true

# Environment-Specific Configurations
environments:
  # Development environment
  development:
    debug_mode: true
    verbose_logging: true
    mock_external_services: true
    use_test_database: true
    
  # Staging environment
  staging:
    debug_mode: false
    verbose_logging: false
    mock_external_services: false
    use_staging_database: true
    
  # Production environment (for production-like testing)
  production:
    debug_mode: false
    verbose_logging: false
    mock_external_services: false
    use_production_database: false
    read_only_mode: true

# Feature Flags for Testing
feature_flags:
  # Advanced testing features
  advanced_performance_testing: true
  chaos_engineering: true
  load_testing: true
  security_testing: true
  
  # Experimental features
  ai_powered_test_generation: false
  automatic_test_healing: false
  predictive_failure_detection: false
  
  # Integration features
  slack_notifications: false
  email_reports: false
  dashboard_integration: false

# Custom Test Categories
test_categories:
  # Functional categories
  smoke_tests:
    timeout: 60
    critical_only: true
    
  regression_tests:
    timeout: 1800
    full_coverage: true
    
  acceptance_tests:
    timeout: 3600
    user_scenarios: true
    
  # Non-functional categories
  performance_tests:
    timeout: 1800
    load_testing: true
    
  security_tests:
    timeout: 900
    vulnerability_scanning: true
    
  compatibility_tests:
    timeout: 1200
    cross_platform: true
