["insights/document/tests/test_agent.py::TestDocumentAgent::test_agent_initialization", "insights/document/tests/test_agent.py::TestDocumentAgent::test_agent_initialization_with_config", "insights/document/tests/test_agent.py::TestDocumentAgent::test_cleanup", "insights/document/tests/test_agent.py::TestDocumentAgent::test_execute_success", "insights/document/tests/test_agent.py::TestDocumentAgent::test_execute_with_errors", "insights/document/tests/test_agent.py::TestDocumentAgent::test_generate_document_from_request", "insights/document/tests/test_agent.py::TestDocumentAgent::test_initialize", "insights/document/tests/test_agent.py::TestDocumentAgent::test_initialize_missing_context", "insights/document/tests/test_agent.py::TestDocumentAgent::test_preview_template", "insights/document/tests/test_agent.py::TestDocumentAgent::test_should_continue_after_generation", "insights/document/tests/test_agent.py::TestDocumentAgent::test_should_continue_after_template_selection", "insights/document/tests/test_agent.py::TestDocumentAgent::test_should_continue_after_variable_collection", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_complete_document_generation_workflow", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_convenience_method", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_missing_template_handling", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_template_preview", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_tenant_template_priority", "insights/document/tests/test_integration.py::TestDocumentAgentIntegration::test_variable_validation_failure", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_extract_variables_from_template", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_format_currency_filter", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_format_date_filter", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_initialization", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_join_template_sections", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_join_template_sections_dict", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_render_global_template_jsonb", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_render_invalid_jinja_template", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_render_jinja_template", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_render_simple_template", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_render_template_with_missing_variables", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_unsupported_template_source", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_validate_variables_empty_required", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_validate_variables_missing_required", "insights/document/tests/test_template_engine.py::TestTemplateEngine::test_validate_variables_success", "interactive/intake/test_basic.py::TestCaseClassifier::test_criminal_defense_classification", "interactive/intake/test_basic.py::TestCaseClassifier::test_empty_description", "interactive/intake/test_basic.py::TestCaseClassifier::test_family_law_classification", "interactive/intake/test_basic.py::TestCaseClassifier::test_personal_injury_classification", "interactive/intake/test_basic.py::TestCaseClassifier::test_urgency_detection", "interactive/intake/test_basic.py::TestConflictChecker::test_basic_conflict_check", "interactive/intake/test_basic.py::TestIntakeAgent::test_agent_initialization", "interactive/intake/test_basic.py::TestIntakeAgent::test_case_classification_utility", "interactive/intake/test_basic.py::TestIntakeAgent::test_supported_practice_areas", "interactive/intake/test_basic.py::TestIntakeAgent::test_workflow_steps", "interactive/intake/test_basic.py::TestIntakeState::test_case_information", "interactive/intake/test_basic.py::TestIntakeState::test_client_information", "interactive/intake/test_basic.py::TestIntakeState::test_state_initialization", "interactive/intake/test_basic.py::TestRouters::test_client_router", "interactive/intake/test_basic.py::TestRouters::test_intake_router", "interactive/intake/test_basic.py::TestRouters::test_staff_router", "interactive/intake/test_basic.py::TestRouters::test_staff_router_with_prefilled_data", "interactive/intake/test_basic.py::test_case_urgency_enum", "interactive/intake/test_basic.py::test_practice_area_enum", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_availability_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_delete_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_schedule_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_update_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_view_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_non_calendar_intent", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_calculation_keywords", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_legal_deadlines", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_negative_cases", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_tracking_keywords", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_actions", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_creation_keywords", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_document_types", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_negative_cases", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_high_confidence", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_medium_confidence", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_no_match", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_pattern_weights", "interactive/tests/test_master_router.py::TestMasterGraphCreation::test_get_workflow_info", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_calendar_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_case_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_client_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_deadline_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_dict_message_format", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_document_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_empty_messages", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_intake_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_no_messages", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_no_user_input", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_research_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_task_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_unknown_intent", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_non_task_intent", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_task_intent_create_keywords", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_task_intent_delete_keywords", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_task_intent_patterns", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_task_intent_read_keywords", "interactive/tests/test_task_routing.py::TestTaskIntentDetection::test_task_intent_update_keywords", "interactive/tests/test_task_routing.py::TestTaskRouting::test_invalid_task_intent", "interactive/tests/test_task_routing.py::TestTaskRouting::test_natural_language_task_create", "interactive/tests/test_task_routing.py::TestTaskRouting::test_natural_language_task_list", "interactive/tests/test_task_routing.py::TestTaskRouting::test_natural_language_task_update", "interactive/tests/test_task_routing.py::TestTaskRouting::test_task_create_dispatch", "interactive/tests/test_task_routing.py::TestTaskRouting::test_task_delete_dispatch", "interactive/tests/test_task_routing.py::TestTaskRouting::test_task_read_dispatch", "interactive/tests/test_task_routing.py::TestTaskRouting::test_task_update_dispatch"]