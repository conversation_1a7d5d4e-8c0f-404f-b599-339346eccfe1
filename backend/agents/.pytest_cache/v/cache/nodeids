["interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_availability_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_delete_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_schedule_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_update_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_calendar_intent_view_keywords", "interactive/tests/test_master_router.py::TestCalendarIntentDetection::test_non_calendar_intent", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_calculation_keywords", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_legal_deadlines", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_negative_cases", "interactive/tests/test_master_router.py::TestDeadlineIntentDetection::test_deadline_intent_tracking_keywords", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_actions", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_creation_keywords", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_document_types", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_explicit_namespace", "interactive/tests/test_master_router.py::TestDocumentIntentDetection::test_document_intent_negative_cases", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_high_confidence", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_medium_confidence", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_no_match", "interactive/tests/test_master_router.py::TestEnhancedKeywordDetection::test_enhanced_keyword_detection_pattern_weights", "interactive/tests/test_master_router.py::TestMasterGraphCreation::test_get_workflow_info", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_calendar_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_case_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_client_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_deadline_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_dict_message_format", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_document_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_empty_messages", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_intake_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_no_messages", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_no_user_input", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_research_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_task_intent", "interactive/tests/test_master_router.py::TestMasterRouter::test_master_router_unknown_intent"]