{"interactive/tests/test_task_routing.py::TestTaskRouting::test_natural_language_task_create": true, "interactive/intake/test_basic.py::TestIntakeState": true, "interactive/intake/test_basic.py::TestCaseClassifier": true, "interactive/intake/test_basic.py::TestConflictChecker": true, "interactive/intake/test_basic.py::TestIntakeAgent": true, "interactive/intake/test_basic.py::TestRouters": true, "interactive/intake/test_basic.py::TestConflictChecker::test_basic_conflict_check": true, "interactive/intake/test_basic.py::TestIntakeAgent::test_case_classification_utility": true}