#!/bin/bash

# Test Environment Setup Script
# This script sets up the testing environment for the agent system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AGENTS_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$AGENTS_DIR")")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Python dependencies
install_python_dependencies() {
    print_status "Installing Python testing dependencies..."
    
    # Check if pip is available
    if ! command_exists pip; then
        print_error "pip is not installed. Please install Python and pip first."
        exit 1
    fi
    
    # Install testing dependencies
    local test_deps=(
        "pytest>=7.0.0"
        "pytest-asyncio>=0.21.0"
        "pytest-cov>=4.0.0"
        "pytest-xdist>=3.0.0"
        "pytest-mock>=3.10.0"
        "pytest-timeout>=2.1.0"
        "psutil>=5.9.0"
        "pyyaml>=6.0"
    )
    
    for dep in "${test_deps[@]}"; do
        print_status "Installing $dep..."
        pip install "$dep" || {
            print_warning "Failed to install $dep, continuing..."
        }
    done
    
    print_success "Python dependencies installed"
}

# Function to create test directories
create_test_directories() {
    print_status "Creating test directory structure..."
    
    local test_dirs=(
        "${AGENTS_DIR}/tests"
        "${AGENTS_DIR}/tests/unit"
        "${AGENTS_DIR}/tests/integration"
        "${AGENTS_DIR}/tests/e2e"
        "${AGENTS_DIR}/tests/performance"
        "${AGENTS_DIR}/test_data"
        "${AGENTS_DIR}/coverage_html"
        "${AGENTS_DIR}/test_reports"
    )
    
    for dir in "${test_dirs[@]}"; do
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    done
    
    print_success "Test directories created"
}

# Function to create test data files
create_test_data_files() {
    print_status "Creating test data files..."
    
    # Create __init__.py files for test directories
    local init_files=(
        "${AGENTS_DIR}/tests/__init__.py"
        "${AGENTS_DIR}/tests/unit/__init__.py"
        "${AGENTS_DIR}/tests/integration/__init__.py"
        "${AGENTS_DIR}/tests/e2e/__init__.py"
        "${AGENTS_DIR}/tests/performance/__init__.py"
    )
    
    for init_file in "${init_files[@]}"; do
        if [[ ! -f "$init_file" ]]; then
            touch "$init_file"
            print_status "Created: $init_file"
        fi
    done
    
    # Create sample test data
    cat > "${AGENTS_DIR}/test_data/sample_scenarios.json" << 'EOF'
{
  "agent_execution": [
    {
      "name": "basic_execution",
      "description": "Basic agent execution test",
      "input_state": {
        "messages": [{"role": "user", "content": "Hello"}],
        "tenant_id": "test-tenant",
        "user_id": "test-user"
      },
      "expected_output": {
        "message_count": 2,
        "last_message_role": "assistant"
      }
    }
  ],
  "error_handling": [
    {
      "name": "network_error",
      "description": "Test network error handling",
      "error_type": "NetworkError",
      "error_message": "Connection failed"
    }
  ]
}
EOF
    
    cat > "${AGENTS_DIR}/test_data/test_config.yaml" << 'EOF'
performance:
  max_execution_time: 5.0
  max_memory_usage: 100.0
  min_success_rate: 0.95

database:
  test_db_url: "sqlite:///:memory:"
  pool_size: 5

external_apis:
  timeout: 30.0
  retry_attempts: 3
EOF
    
    print_success "Test data files created"
}

# Function to create sample test files
create_sample_test_files() {
    print_status "Creating sample test files..."
    
    # Create sample unit test
    cat > "${AGENTS_DIR}/tests/unit/test_base_agent.py" << 'EOF'
"""
Sample unit test for base agent functionality.
"""

import pytest
from unittest.mock import MagicMock

from backend.agents.shared.testing import BaseAgentTest, StateFactory, AgentAssertions


class TestBaseAgent(BaseAgentTest):
    """Test base agent functionality."""
    
    @pytest.mark.unit
    def test_state_creation(self):
        """Test state creation."""
        state = StateFactory.create_empty_state()
        AgentAssertions.assert_state_valid(state)
    
    @pytest.mark.unit
    async def test_agent_execution(self):
        """Test basic agent execution."""
        # This is a placeholder test
        assert True
EOF
    
    # Create sample integration test
    cat > "${AGENTS_DIR}/tests/integration/test_agent_workflow.py" << 'EOF'
"""
Sample integration test for agent workflows.
"""

import pytest
from backend.agents.shared.testing import BaseAgentTest


class TestAgentWorkflow(BaseAgentTest):
    """Test agent workflow integration."""
    
    @pytest.mark.integration
    async def test_full_workflow(self):
        """Test complete agent workflow."""
        # This is a placeholder test
        assert True
EOF
    
    # Create sample performance test
    cat > "${AGENTS_DIR}/tests/performance/test_agent_performance.py" << 'EOF'
"""
Sample performance test for agents.
"""

import pytest
from backend.agents.shared.testing import BaseAgentTest, PerformanceTester


class TestAgentPerformance(BaseAgentTest):
    """Test agent performance."""
    
    @pytest.mark.performance
    async def test_execution_performance(self, performance_tester):
        """Test agent execution performance."""
        # This is a placeholder test
        assert True
EOF
    
    print_success "Sample test files created"
}

# Function to setup environment variables
setup_environment_variables() {
    print_status "Setting up environment variables..."
    
    # Create .env.test file if it doesn't exist
    local env_test_file="${PROJECT_ROOT}/.env.test"
    
    if [[ ! -f "$env_test_file" ]]; then
        cat > "$env_test_file" << 'EOF'
# Test Environment Variables
TESTING=true
LOG_LEVEL=DEBUG
TEST_DATABASE_URL=sqlite:///:memory:

# Mock API Keys (for testing only)
VOYAGE_API_KEY=test-voyage-api-key
PINECONE_API_KEY=test-pinecone-api-key
OPENAI_API_KEY=test-openai-api-key
SUPABASE_URL=https://test.supabase.co
SUPABASE_KEY=test-supabase-key
SUPABASE_JWT_SECRET=test-jwt-secret
EOF
        print_success "Created .env.test file"
    else
        print_status ".env.test file already exists"
    fi
}

# Function to validate setup
validate_setup() {
    print_status "Validating test setup..."
    
    # Check if pytest can be imported
    if python -c "import pytest" 2>/dev/null; then
        print_success "pytest is available"
    else
        print_error "pytest is not available"
        return 1
    fi
    
    # Check if test directories exist
    local required_dirs=(
        "${AGENTS_DIR}/tests"
        "${AGENTS_DIR}/shared/testing"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            print_success "Directory exists: $dir"
        else
            print_error "Directory missing: $dir"
            return 1
        fi
    done
    
    # Check if configuration files exist
    local required_files=(
        "${AGENTS_DIR}/conftest.py"
        "${AGENTS_DIR}/pytest.ini"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "File exists: $file"
        else
            print_error "File missing: $file"
            return 1
        fi
    done
    
    print_success "Test setup validation completed"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Set up the testing environment for the agent system.

OPTIONS:
    --skip-deps     Skip Python dependency installation
    --skip-data     Skip test data creation
    --validate-only Only validate existing setup
    -h, --help      Show this help message

EXAMPLES:
    $0              # Full setup
    $0 --skip-deps  # Setup without installing dependencies
    $0 --validate-only # Only validate existing setup

EOF
}

# Parse command line arguments
SKIP_DEPS=false
SKIP_DATA=false
VALIDATE_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --skip-data)
            SKIP_DATA=true
            shift
            ;;
        --validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "🔧 Setting up Agent System Test Environment"
    echo
    
    if [[ "$VALIDATE_ONLY" == "true" ]]; then
        validate_setup
        print_success "✅ Test environment validation completed!"
        return 0
    fi
    
    # Install dependencies
    if [[ "$SKIP_DEPS" != "true" ]]; then
        install_python_dependencies
    else
        print_warning "Skipping dependency installation"
    fi
    
    # Create directories
    create_test_directories
    
    # Create test data
    if [[ "$SKIP_DATA" != "true" ]]; then
        create_test_data_files
        create_sample_test_files
    else
        print_warning "Skipping test data creation"
    fi
    
    # Setup environment
    setup_environment_variables
    
    # Validate setup
    validate_setup
    
    print_success "🎉 Test environment setup completed successfully!"
    print_status "You can now run tests using: ./scripts/run_tests.sh"
}

# Run main function
main "$@"
