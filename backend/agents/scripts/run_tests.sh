#!/bin/bash

# Agent System Test Runner
# This script runs comprehensive tests for the agent system with proper environment setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AGENTS_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$AGENTS_DIR")")"

# Default values
TEST_TYPE="all"
COVERAGE_REPORT="term-missing"
VERBOSE=false
PARALLEL=false
PERFORMANCE_TESTS=false
INTEGRATION_TESTS=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Run comprehensive tests for the agent system.

OPTIONS:
    -t, --type TYPE         Test type: unit, integration, e2e, performance, all (default: all)
    -c, --coverage FORMAT   Coverage report format: term, term-missing, html, xml (default: term-missing)
    -v, --verbose           Enable verbose output
    -p, --parallel          Run tests in parallel
    -f, --performance       Include performance tests
    -i, --integration       Include integration tests
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Run all tests with default settings
    $0 -t unit -v           # Run unit tests with verbose output
    $0 -t integration -c html # Run integration tests with HTML coverage
    $0 -p -f                # Run all tests in parallel with performance tests

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE_REPORT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        -f|--performance)
            PERFORMANCE_TESTS=true
            shift
            ;;
        -i|--integration)
            INTEGRATION_TESTS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Set environment variables
    export TESTING=true
    export LOG_LEVEL=DEBUG
    export PYTHONPATH="${PROJECT_ROOT}/src:${PROJECT_ROOT}/backend:${PYTHONPATH}"
    
    # Create test directories if they don't exist
    mkdir -p "${AGENTS_DIR}/tests/unit"
    mkdir -p "${AGENTS_DIR}/tests/integration"
    mkdir -p "${AGENTS_DIR}/tests/e2e"
    mkdir -p "${AGENTS_DIR}/coverage_html"
    
    print_success "Test environment setup complete"
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check if pytest is installed
    if ! command -v pytest &> /dev/null; then
        print_error "pytest is not installed. Please install it with: pip install pytest"
        exit 1
    fi
    
    # Check if coverage is installed
    if ! python -c "import pytest_cov" &> /dev/null; then
        print_warning "pytest-cov is not installed. Coverage reporting will be disabled."
        COVERAGE_REPORT="none"
    fi
    
    print_success "Dependencies check complete"
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    local pytest_args=()
    pytest_args+=("-m" "unit or not (integration or e2e or performance)")
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_args+=("-v")
    fi
    
    if [[ "$PARALLEL" == "true" ]]; then
        pytest_args+=("-n" "auto")
    fi
    
    if [[ "$COVERAGE_REPORT" != "none" ]]; then
        pytest_args+=("--cov=backend/agents")
        pytest_args+=("--cov-report=$COVERAGE_REPORT")
    fi
    
    cd "$AGENTS_DIR"
    python -m pytest "${pytest_args[@]}" tests/unit/ || return 1
    
    print_success "Unit tests completed"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    local pytest_args=()
    pytest_args+=("-m" "integration")
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_args+=("-v")
    fi
    
    cd "$AGENTS_DIR"
    python -m pytest "${pytest_args[@]}" tests/integration/ || return 1
    
    print_success "Integration tests completed"
}

# Function to run e2e tests
run_e2e_tests() {
    print_status "Running end-to-end tests..."
    
    local pytest_args=()
    pytest_args+=("-m" "e2e")
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_args+=("-v")
    fi
    
    cd "$AGENTS_DIR"
    python -m pytest "${pytest_args[@]}" tests/e2e/ || return 1
    
    print_success "End-to-end tests completed"
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    local pytest_args=()
    pytest_args+=("-m" "performance")
    pytest_args+=("--tb=short")
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_args+=("-v")
    fi
    
    cd "$AGENTS_DIR"
    python -m pytest "${pytest_args[@]}" tests/ || return 1
    
    print_success "Performance tests completed"
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests..."
    
    local pytest_args=()
    
    if [[ "$VERBOSE" == "true" ]]; then
        pytest_args+=("-v")
    fi
    
    if [[ "$PARALLEL" == "true" ]]; then
        pytest_args+=("-n" "auto")
    fi
    
    if [[ "$COVERAGE_REPORT" != "none" ]]; then
        pytest_args+=("--cov=backend/agents")
        pytest_args+=("--cov-report=$COVERAGE_REPORT")
        if [[ "$COVERAGE_REPORT" == "html" ]]; then
            pytest_args+=("--cov-report=html:coverage_html")
        fi
    fi
    
    # Exclude performance tests unless explicitly requested
    if [[ "$PERFORMANCE_TESTS" != "true" ]]; then
        pytest_args+=("-m" "not performance")
    fi
    
    cd "$AGENTS_DIR"
    python -m pytest "${pytest_args[@]}" || return 1
    
    print_success "All tests completed"
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    if [[ "$COVERAGE_REPORT" == "html" ]]; then
        print_success "HTML coverage report generated at: ${AGENTS_DIR}/coverage_html/index.html"
    fi
    
    if [[ "$COVERAGE_REPORT" == "xml" ]]; then
        print_success "XML coverage report generated at: ${AGENTS_DIR}/coverage.xml"
    fi
}

# Main execution
main() {
    print_status "🧪 Starting Agent System Tests"
    print_status "Test type: $TEST_TYPE"
    print_status "Coverage: $COVERAGE_REPORT"
    print_status "Verbose: $VERBOSE"
    print_status "Parallel: $PARALLEL"
    print_status "Performance tests: $PERFORMANCE_TESTS"
    print_status "Integration tests: $INTEGRATION_TESTS"
    echo
    
    # Setup
    setup_test_environment
    check_dependencies
    
    # Run tests based on type
    case "$TEST_TYPE" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "all")
            run_all_tests
            ;;
        *)
            print_error "Invalid test type: $TEST_TYPE"
            show_usage
            exit 1
            ;;
    esac
    
    # Generate report
    generate_test_report
    
    print_success "🎉 All tests completed successfully!"
}

# Run main function
main "$@"
