"""
Intake Agent Package

This package provides a unified intake agent that supports multiple practice areas:
- Personal Injury: Auto accidents, slip/fall, medical malpractice, product liability
- Family Law: Divorce, child custody, adoption, domestic relations
- Criminal Defense: DUI/DWI, misdemeanors, felonies, traffic violations

The agent handles complete client intake processes with practice-specific workflows,
conflict checking, and urgency handling.

Usage:
    from backend.agents.interactive.intake import IntakeAgent, intake_router
    
    # Create intake agent
    agent = IntakeAgent()
    
    # Use router for different entry points
    result = await intake_router(state, config)
"""

from .agent import IntakeAgent
from .router import intake_router, intake_client_router, intake_staff_router
from .state import IntakeState, PracticeArea, CaseUrgency, WorkType
from .case_classifier import MultiPracticeCaseClassifier
from .conflict_checker import MultiPracticeConflictChecker
from .nodes import IntakeNodes

__all__ = [
    "IntakeAgent",
    "intake_router",
    "intake_client_router",
    "intake_staff_router",
    "IntakeState",
    "PracticeArea",
    "CaseUrgency",
    "WorkType",
    "MultiPracticeCaseClassifier",
    "MultiPracticeConflict<PERSON>hecker",
    "IntakeNodes",
]
