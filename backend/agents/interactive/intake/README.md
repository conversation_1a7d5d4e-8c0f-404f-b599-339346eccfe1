# Multi-Practice Intake Agent

The Multi-Practice Intake Agent is a consolidated agent that supports client intake processes for Personal Injury, Family Law, and Criminal Defense practice areas. It provides intelligent case classification, practice-specific workflows, comprehensive conflict checking, and urgency-based processing.

## Features

### Multi-Practice Area Support
- **Personal Injury**: Auto accidents, slip/fall, medical malpractice, product liability, workplace injuries, wrongful death
- **Family Law**: Divorce, child custody, child support, adoption, domestic violence, prenuptial agreements, paternity
- **Criminal Defense**: DUI/DWI, misdemeanors, felonies, traffic violations, white collar crimes, drug offenses

### Intelligent Matter Classification
- **LLM-Powered Reasoning**: Uses GPT-4 for sophisticated legal analysis beyond keyword matching
- **Semantic Understanding**: Comprehends legal context, relationships, and nuances
- **Multi-Factor Analysis**: Considers facts, timeline, parties, damages, complexity, and red flags
- **Automatic practice area detection** with confidence scoring and detailed reasoning
- **Matter type classification** within each practice area using legal expertise
- **Urgency level assessment** based on timeline factors and legal deadlines
- **Fallback Protection**: Keyword-based classification as backup for reliability

### Practice-Specific Workflows
- Customized data collection for each practice area
- Practice-specific validation rules and required fields
- Tailored follow-up questions and prompts
- Specialized conflict checking rules per practice area

### Comprehensive Conflict Checking
- Multi-practice conflict detection rules
- Name variation and alias checking
- Practice-specific conflict scenarios (opposing parties, family members, co-defendants)
- Severity assessment and guidance

### Urgency-Based Processing
- **Critical**: Immediate attention (e.g., court date tomorrow)
- **High**: Priority processing (e.g., recent arrest, upcoming deadlines)
- **Medium**: Expedited processing (e.g., criminal cases, domestic violence)
- **Low**: Standard processing

## Architecture

### Core Components

#### IntakeAgent (`agent.py`)
The main consolidated agent that orchestrates the entire intake process.

```python
from backend.agents.interactive.intake import IntakeAgent

agent = IntakeAgent()
result = await agent.invoke(state, config)
```

#### IntakeState (`state.py`)
Enhanced state management with practice-specific fields and validation.

```python
from backend.agents.interactive.intake import IntakeState, PracticeArea

state = IntakeState(
    client=ClientInformation(name="John Doe"),
    case=CaseInformation(practice_area=PracticeArea.PERSONAL_INJURY)
)
```

#### IntelligentMatterClassifier (`intelligent_matter_classifier.py`)
Advanced LLM-powered matter classification with sophisticated legal reasoning.

```python
from backend.agents.interactive.intake import IntelligentMatterClassifier

classifier = IntelligentMatterClassifier()
result = await classifier.classify_matter(
    "My ex-husband hasn't paid child support and is threatening to take the kids",
    {"children_involved": True, "domestic_violence_history": True}
)

# Enhanced result includes:
# - practice_area, case_type, urgency, confidence
# - key_factors: ["Child support violation", "Custody threat", "Safety concerns"]
# - timeline_factors: ["6 months non-payment", "Immediate threat"]
# - parties_involved: ["Ex-husband", "Children", "Client"]
# - red_flags: ["Domestic violence history", "Child safety concerns"]
```

#### MultiPracticeCaseClassifier (`case_classifier.py`)
Fallback keyword-based classification for reliability.

```python
from backend.agents.interactive.intake import MultiPracticeCaseClassifier

classifier = MultiPracticeCaseClassifier()
result = classifier.classify_case("I was in a car accident last week")
```

#### MultiPracticeConflictChecker (`conflict_checker.py`)
Comprehensive conflict checking with practice-specific rules.

```python
from backend.agents.interactive.intake import MultiPracticeConflictChecker

checker = MultiPracticeConflictChecker()
conflicts = await checker.check_conflicts(state, tenant_id)
```

#### IntakeNodes (`nodes.py`)
Workflow nodes that handle different steps of the intake process.

### Workflow Steps

1. **Initial Contact**: Welcome and gather basic client name
2. **Collect Personal Info**: Email, phone, and contact information
3. **Collect Case Details**: Case description and initial classification
4. **Practice Area Details**: Practice-specific information collection
5. **Check Conflicts**: Comprehensive conflict of interest checking
6. **Summarize and Confirm**: Review collected information with client
7. **Save Client Info**: Create case and client records

## Usage

### Basic Usage

```python
from backend.agents.interactive.intake import IntakeAgent, intake_router

# Initialize agent
agent = IntakeAgent()

# Route request
state = await intake_router(initial_state, config)

# Execute intake process
result = await agent.invoke(state, config)
```

### Client Intake

```python
from backend.agents.interactive.intake import intake_client_router

# Route to client intake
state = await intake_client_router(state, config)
```

### Staff Intake

```python
from backend.agents.interactive.intake import intake_staff_router

# Route to staff intake with pre-filled data support
state = await intake_staff_router(state, config)
```

### Practice Area Specific Routing

```python
from backend.agents.interactive.intake import practice_area_router, PracticeArea

# Route to specific practice area
state = await practice_area_router(state, config, PracticeArea.CRIMINAL_DEFENSE)
```

## Configuration

### Practice Area Configuration

Each practice area has specific configuration for required fields, urgency factors, and conflict rules:

```python
practice_area_configs = {
    PracticeArea.PERSONAL_INJURY: {
        "required_fields": ["incident_date", "injuries"],
        "urgency_factors": ["statute_of_limitations", "insurance_deadlines"],
        "conflict_rules": ["opposing_parties", "insurance_companies"]
    },
    PracticeArea.FAMILY_LAW: {
        "required_fields": ["children_involved", "assets_involved"],
        "urgency_factors": ["domestic_violence", "custody_deadlines"],
        "conflict_rules": ["spouse", "family_members", "children"]
    },
    PracticeArea.CRIMINAL_DEFENSE: {
        "required_fields": ["charges", "arrest_date"],
        "urgency_factors": ["court_date", "custody_status"],
        "conflict_rules": ["co_defendants", "victims", "witnesses"]
    }
}
```

### Urgency Handling

The system automatically assesses urgency based on:

- **Criminal Defense**: Court dates, arrest dates, custody status
- **Family Law**: Domestic violence indicators, custody deadlines
- **Personal Injury**: Statute of limitations, insurance deadlines

## State Management

The intake agent uses an enhanced state model that extends the base LangGraph state:

```python
class IntakeState(BaseLangGraphState):
    # Intake flow control
    intake_mode: str = "client"  # "client" or "staff"
    current_step: str = "initial_contact"
    completed_steps: List[str] = []
    
    # Client and case information
    client: ClientInformation
    case: CaseInformation
    
    # Conflict checking
    conflict_check: Optional[ConflictCheckResult]
    
    # Practice area specific context
    practice_area_context: Dict[str, Any]
    
    # Validation and workflow
    validation_errors: List[str]
    can_proceed: bool = True
    requires_immediate_attention: bool = False
```

## Error Handling

The agent includes comprehensive error handling:

- **Validation Errors**: Missing required fields, invalid data
- **Conflict Check Errors**: Graceful degradation if conflict checking fails
- **Classification Errors**: Fallback to default classification
- **State Errors**: Recovery mechanisms for corrupted state

## Testing

The intake agent includes comprehensive test coverage:

```bash
# Run intake agent tests
python -m pytest backend/agents/interactive/intake/tests/

# Run specific test categories
python -m pytest backend/agents/interactive/intake/tests/test_classification.py
python -m pytest backend/agents/interactive/intake/tests/test_conflicts.py
python -m pytest backend/agents/interactive/intake/tests/test_workflows.py
```

## Integration

### Database Integration

The agent integrates with the tenant database for:
- Client record creation
- Case/matter creation
- Conflict checking against existing clients
- Historical case data

### External Services

- **Email Notifications**: Confirmation emails and urgent case alerts
- **SMS Notifications**: Critical case notifications
- **Calendar Integration**: Court date tracking
- **Document Generation**: Intake forms and agreements

## Security Considerations

- **Tenant Isolation**: All data is properly isolated by tenant
- **Data Validation**: Input sanitization and validation
- **Conflict Checking**: Comprehensive conflict of interest detection
- **Audit Logging**: Complete audit trail of intake activities
- **Access Control**: Role-based access for staff vs client interfaces

## Performance

- **Response Time**: < 5 seconds for interactive operations
- **Scalability**: Supports concurrent intake sessions
- **Caching**: Intelligent caching of classification and conflict results
- **Resource Management**: Efficient memory and CPU usage

## Future Enhancements

- **NLP Enhancement**: Advanced natural language processing for better classification
- **Machine Learning**: Learning from historical intake data
- **Integration Expansion**: Additional practice areas and external systems
- **Mobile Optimization**: Enhanced mobile intake experience
- **Voice Integration**: Voice-based intake capabilities
