"""
Multi-Practice Conflict Checker

This module provides comprehensive conflict checking for multiple practice areas,
with practice-specific rules and considerations for different types of legal matters.

Key Features:
- Practice area specific conflict checking rules
- Client name and entity matching
- Opposing party identification
- Jurisdiction-specific requirements
- Conflict severity assessment
- Detailed conflict reporting
"""

import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .state import PracticeArea, IntakeState, ConflictCheckResult


class ConflictSeverity(str, Enum):
    """Severity levels for conflicts."""
    LOW = "low"           # Minor conflict, may be waivable
    MEDIUM = "medium"     # Significant conflict, requires review
    HIGH = "high"         # Serious conflict, likely not waivable
    CRITICAL = "critical" # Absolute conflict, cannot represent


@dataclass
class ConflictMatch:
    """Individual conflict match result."""
    conflict_type: str
    severity: ConflictSeverity
    description: str
    existing_matter_id: Optional[str] = None
    existing_client_name: Optional[str] = None
    match_confidence: float = 0.0
    practice_area_specific: Dict[str, Any] = None


class MultiPracticeConflictChecker:
    """
    Enhanced conflict checker for multiple practice areas.
    
    Provides practice-area specific conflict checking with different
    rules and considerations for each type of legal matter.
    """
    
    def __init__(self):
        """Initialize the conflict checker."""
        self.practice_area_rules = {
            PracticeArea.PERSONAL_INJURY: self._personal_injury_rules,
            PracticeArea.FAMILY_LAW: self._family_law_rules,
            PracticeArea.CRIMINAL_DEFENSE: self._criminal_defense_rules
        }
    
    async def check_conflicts(
        self, 
        state: IntakeState,
        tenant_id: str
    ) -> ConflictCheckResult:
        """
        Perform comprehensive conflict checking for the intake.
        
        Args:
            state: Current intake state
            tenant_id: Tenant ID for database queries
            
        Returns:
            ConflictCheckResult with detailed conflict information
        """
        conflicts = []
        practice_area_checks = {}
        
        try:
            # Get practice area specific rules
            if state.case.practice_area in self.practice_area_rules:
                rule_func = self.practice_area_rules[state.case.practice_area]
                area_conflicts, area_checks = await rule_func(state, tenant_id)
                conflicts.extend(area_conflicts)
                practice_area_checks[state.case.practice_area.value] = area_checks
            
            # Perform general conflict checks
            general_conflicts = await self._general_conflict_checks(state, tenant_id)
            conflicts.extend(general_conflicts)
            
            # Check for name variations and aliases
            name_conflicts = await self._check_name_variations(state, tenant_id)
            conflicts.extend(name_conflicts)
            
            # Assess overall conflict status
            has_conflicts = len(conflicts) > 0
            
            return ConflictCheckResult(
                has_conflicts=has_conflicts,
                conflicts=[self._conflict_to_dict(c) for c in conflicts],
                checked_at=datetime.utcnow(),
                practice_area_specific_checks=practice_area_checks
            )
            
        except Exception as e:
            # Return error state but don't fail the intake
            return ConflictCheckResult(
                has_conflicts=False,
                conflicts=[{
                    "conflict_type": "system_error",
                    "severity": "low",
                    "description": f"Error during conflict check: {str(e)}",
                    "match_confidence": 0.0
                }],
                checked_at=datetime.utcnow(),
                practice_area_specific_checks={"error": str(e)}
            )
    
    async def _personal_injury_rules(
        self, 
        state: IntakeState, 
        tenant_id: str
    ) -> tuple[List[ConflictMatch], Dict[str, Any]]:
        """Personal injury specific conflict checking rules."""
        conflicts = []
        checks = {
            "opposing_parties_checked": False,
            "insurance_companies_checked": False,
            "medical_providers_checked": False
        }
        
        # Check for opposing parties in auto accidents
        if hasattr(state.case, 'case_type') and state.case.case_type == "auto_accident":
            # Look for other driver names in description
            opposing_parties = self._extract_opposing_parties(state.case.description or "")
            for party in opposing_parties:
                existing_conflicts = await self._check_existing_client(party, tenant_id)
                conflicts.extend(existing_conflicts)
            checks["opposing_parties_checked"] = True
        
        # Check insurance companies
        if state.case.insurance_involved:
            # Extract insurance company names from description
            insurance_companies = self._extract_insurance_companies(state.case.description or "")
            for company in insurance_companies:
                # Check if we represent this insurance company
                insurance_conflicts = await self._check_insurance_representation(company, tenant_id)
                conflicts.extend(insurance_conflicts)
            checks["insurance_companies_checked"] = True
        
        # Check medical providers for malpractice cases
        if hasattr(state.case, 'case_type') and state.case.case_type == "medical_malpractice":
            medical_providers = self._extract_medical_providers(state.case.description or "")
            for provider in medical_providers:
                provider_conflicts = await self._check_medical_provider_representation(provider, tenant_id)
                conflicts.extend(provider_conflicts)
            checks["medical_providers_checked"] = True
        
        return conflicts, checks
    
    async def _family_law_rules(
        self, 
        state: IntakeState, 
        tenant_id: str
    ) -> tuple[List[ConflictMatch], Dict[str, Any]]:
        """Family law specific conflict checking rules."""
        conflicts = []
        checks = {
            "spouse_checked": False,
            "children_checked": False,
            "family_members_checked": False
        }
        
        # Extract spouse/partner information
        spouse_names = self._extract_spouse_names(state.case.description or "")
        for spouse in spouse_names:
            spouse_conflicts = await self._check_existing_client(spouse, tenant_id)
            # Family law conflicts are typically high severity
            for conflict in spouse_conflicts:
                conflict.severity = ConflictSeverity.HIGH
            conflicts.extend(spouse_conflicts)
        checks["spouse_checked"] = True
        
        # Check for children's interests conflicts
        if state.case.children_involved:
            # In custody cases, check if we've represented the other parent
            children_conflicts = await self._check_children_representation(state, tenant_id)
            conflicts.extend(children_conflicts)
            checks["children_checked"] = True
        
        # Check extended family members
        family_members = self._extract_family_members(state.case.description or "")
        for member in family_members:
            family_conflicts = await self._check_existing_client(member, tenant_id)
            conflicts.extend(family_conflicts)
        checks["family_members_checked"] = True
        
        return conflicts, checks
    
    async def _criminal_defense_rules(
        self, 
        state: IntakeState, 
        tenant_id: str
    ) -> tuple[List[ConflictMatch], Dict[str, Any]]:
        """Criminal defense specific conflict checking rules."""
        conflicts = []
        checks = {
            "co_defendants_checked": False,
            "victims_checked": False,
            "prosecution_witnesses_checked": False,
            "jurisdiction_checked": False
        }
        
        # Check for co-defendants
        co_defendants = self._extract_co_defendants(state.case.description or "")
        for defendant in co_defendants:
            codef_conflicts = await self._check_existing_client(defendant, tenant_id)
            # Co-defendant conflicts can be complex
            for conflict in codef_conflicts:
                conflict.severity = ConflictSeverity.MEDIUM
                conflict.description += " (Co-defendant conflict - may require separate counsel)"
            conflicts.extend(codef_conflicts)
        checks["co_defendants_checked"] = True
        
        # Check for victims we may have represented
        victims = self._extract_victims(state.case.description or "")
        for victim in victims:
            victim_conflicts = await self._check_existing_client(victim, tenant_id)
            # Victim conflicts are typically critical
            for conflict in victim_conflicts:
                conflict.severity = ConflictSeverity.CRITICAL
            conflicts.extend(victim_conflicts)
        checks["victims_checked"] = True
        
        # Check prosecution witnesses
        witnesses = self._extract_witnesses(state.case.description or "")
        for witness in witnesses:
            witness_conflicts = await self._check_existing_client(witness, tenant_id)
            conflicts.extend(witness_conflicts)
        checks["prosecution_witnesses_checked"] = True
        
        # Jurisdiction-specific checks (placeholder for future implementation)
        checks["jurisdiction_checked"] = True
        
        return conflicts, checks
    
    async def _general_conflict_checks(
        self, 
        state: IntakeState, 
        tenant_id: str
    ) -> List[ConflictMatch]:
        """General conflict checks applicable to all practice areas."""
        conflicts = []
        
        # Check client name against existing clients
        if state.client.name:
            existing_conflicts = await self._check_existing_client(state.client.name, tenant_id)
            conflicts.extend(existing_conflicts)
        
        # Check email against existing clients
        if state.client.email:
            email_conflicts = await self._check_existing_email(state.client.email, tenant_id)
            conflicts.extend(email_conflicts)
        
        return conflicts
    
    async def _check_existing_client(self, name: str, tenant_id: str) -> List[ConflictMatch]:
        """Check if name matches existing client."""
        # This would query the database for existing clients
        # For now, return empty list (placeholder implementation)
        # TODO: Implement actual database query
        return []
    
    async def _check_existing_email(self, email: str, tenant_id: str) -> List[ConflictMatch]:
        """Check if email matches existing client."""
        # This would query the database for existing client emails
        # For now, return empty list (placeholder implementation)
        # TODO: Implement actual database query
        return []
    
    async def _check_name_variations(
        self, 
        state: IntakeState, 
        tenant_id: str
    ) -> List[ConflictMatch]:
        """Check for name variations and aliases."""
        conflicts = []
        
        if not state.client.name:
            return conflicts
        
        # Generate name variations
        variations = self._generate_name_variations(state.client.name)
        
        for variation in variations:
            variation_conflicts = await self._check_existing_client(variation, tenant_id)
            conflicts.extend(variation_conflicts)
        
        return conflicts
    
    def _generate_name_variations(self, name: str) -> List[str]:
        """Generate common name variations."""
        variations = []
        
        # Split name into parts
        parts = name.strip().split()
        if len(parts) < 2:
            return variations
        
        # First Last -> Last, First
        if len(parts) == 2:
            variations.append(f"{parts[1]}, {parts[0]}")
        
        # Add initials variations
        if len(parts) >= 2:
            variations.append(f"{parts[0][0]}. {parts[-1]}")
            variations.append(f"{parts[0]} {parts[-1][0]}.")
        
        return variations
    
    def _extract_opposing_parties(self, description: str) -> List[str]:
        """Extract opposing party names from case description."""
        # Simple pattern matching for names
        # This could be enhanced with NLP
        patterns = [
            r"other driver (\w+ \w+)",
            r"defendant (\w+ \w+)",
            r"vs\.? (\w+ \w+)",
            r"against (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))  # Remove duplicates
    
    def _extract_spouse_names(self, description: str) -> List[str]:
        """Extract spouse/partner names from description."""
        patterns = [
            r"husband (\w+ \w+)",
            r"wife (\w+ \w+)",
            r"spouse (\w+ \w+)",
            r"partner (\w+ \w+)",
            r"ex-husband (\w+ \w+)",
            r"ex-wife (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))
    
    def _extract_co_defendants(self, description: str) -> List[str]:
        """Extract co-defendant names from description."""
        patterns = [
            r"co-defendant (\w+ \w+)",
            r"also charged (\w+ \w+)",
            r"along with (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))
    
    def _extract_victims(self, description: str) -> List[str]:
        """Extract victim names from description."""
        patterns = [
            r"victim (\w+ \w+)",
            r"complainant (\w+ \w+)",
            r"alleged victim (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))
    
    def _extract_witnesses(self, description: str) -> List[str]:
        """Extract witness names from description."""
        patterns = [
            r"witness (\w+ \w+)",
            r"testified (\w+ \w+)",
            r"saw (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))
    
    def _extract_insurance_companies(self, description: str) -> List[str]:
        """Extract insurance company names from description."""
        # Common insurance company patterns
        patterns = [
            r"(State Farm|Geico|Progressive|Allstate|Farmers|USAA|Liberty Mutual)",
            r"(\w+ Insurance)",
            r"insured by (\w+)"
        ]
        
        companies = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            companies.extend(matches)
        
        return list(set(companies))
    
    def _extract_medical_providers(self, description: str) -> List[str]:
        """Extract medical provider names from description."""
        patterns = [
            r"Dr\. (\w+ \w+)",
            r"Doctor (\w+ \w+)",
            r"(\w+ Hospital)",
            r"(\w+ Medical Center)"
        ]
        
        providers = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            providers.extend(matches)
        
        return list(set(providers))
    
    def _extract_family_members(self, description: str) -> List[str]:
        """Extract family member names from description."""
        patterns = [
            r"mother (\w+ \w+)",
            r"father (\w+ \w+)",
            r"brother (\w+ \w+)",
            r"sister (\w+ \w+)",
            r"son (\w+ \w+)",
            r"daughter (\w+ \w+)"
        ]
        
        names = []
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            names.extend(matches)
        
        return list(set(names))
    
    async def _check_insurance_representation(self, company: str, tenant_id: str) -> List[ConflictMatch]:
        """Check if we represent the insurance company."""
        # Placeholder for database query
        return []
    
    async def _check_medical_provider_representation(self, provider: str, tenant_id: str) -> List[ConflictMatch]:
        """Check if we represent the medical provider."""
        # Placeholder for database query
        return []
    
    async def _check_children_representation(self, state: IntakeState, tenant_id: str) -> List[ConflictMatch]:
        """Check for children's interests conflicts."""
        # Placeholder for complex children's interests analysis
        return []
    
    def _conflict_to_dict(self, conflict: ConflictMatch) -> Dict[str, Any]:
        """Convert ConflictMatch to dictionary."""
        return {
            "conflict_type": conflict.conflict_type,
            "severity": conflict.severity.value,
            "description": conflict.description,
            "existing_matter_id": conflict.existing_matter_id,
            "existing_client_name": conflict.existing_client_name,
            "match_confidence": conflict.match_confidence,
            "practice_area_specific": conflict.practice_area_specific or {}
        }
