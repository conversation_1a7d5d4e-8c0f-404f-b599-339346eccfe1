"""
Consolidated Multi-Practice Intake Agent

This module implements the main intake agent that consolidates support for
Personal Injury, Family Law, and Criminal Defense practice areas with
complete functionality for client intake processes.

Key Features:
- Unified agent supporting multiple practice areas
- Practice-specific workflows and validation
- Intelligent case classification and routing
- Comprehensive conflict checking
- Urgency-based processing
- Complete intake lifecycle management
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from langchain_core.runnables import RunnableConfig
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from backend.agents.shared.core.base_agent import BaseAgent
from backend.agents.shared.core.state import AgentExecutionContext, AgentStatus
from .state import IntakeState, PracticeArea, WorkType, CaseUrgency
from .nodes import IntakeNodes
from .case_classifier import MultiPracticeCaseClassifier
from .intelligent_matter_classifier import IntelligentMatterClassifier
from .conflict_checker import MultiPracticeConflictChecker

logger = logging.getLogger(__name__)


class IntakeAgent(BaseAgent):
    """
    Consolidated intake agent for multiple practice areas.
    
    This agent handles the complete client intake process for Personal Injury,
    Family Law, and Criminal Defense cases with practice-specific workflows,
    intelligent classification, and comprehensive conflict checking.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the intake agent.
        
        Args:
            config: Agent configuration
        """
        super().__init__(
            agent_type="intake",
            agent_name="IntakeAgent",
            config=config or {}
        )
        
        # Initialize components
        self.nodes = IntakeNodes()
        self.classifier = MultiPracticeCaseClassifier()  # Fallback classifier
        self.intelligent_classifier = IntelligentMatterClassifier()  # Primary LLM classifier
        self.conflict_checker = MultiPracticeConflictChecker()
        
        # Workflow configuration
        self.workflow_steps = [
            "initial_contact",
            "collect_personal_info", 
            "collect_case_details",
            "practice_area_details",
            "check_conflicts",
            "summarize_and_confirm",
            "save_client_info"
        ]
        
        # Practice area specific configurations
        self.practice_area_configs = {
            PracticeArea.PERSONAL_INJURY: {
                "required_fields": ["incident_date", "injuries"],
                "urgency_factors": ["statute_of_limitations", "insurance_deadlines"],
                "conflict_rules": ["opposing_parties", "insurance_companies"]
            },
            PracticeArea.FAMILY_LAW: {
                "required_fields": ["children_involved", "assets_involved"],
                "urgency_factors": ["domestic_violence", "custody_deadlines"],
                "conflict_rules": ["spouse", "family_members", "children"]
            },
            PracticeArea.CRIMINAL_DEFENSE: {
                "required_fields": ["charges", "arrest_date"],
                "urgency_factors": ["court_date", "custody_status"],
                "conflict_rules": ["co_defendants", "victims", "witnesses"]
            }
        }
    
    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Initialize the intake agent with mode-specific configuration.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Initializing consolidated intake agent")
        
        # Set default intake mode if not specified
        if "intake_mode" not in state:
            state["intake_mode"] = "client"
        
        # Initialize intake state structure
        if "current_step" not in state:
            state["current_step"] = "initial_contact"
        
        if "completed_steps" not in state:
            state["completed_steps"] = []
        
        if "client" not in state:
            state["client"] = {}
        
        if "matter" not in state:
            state["matter"] = {}
        
        # Set initial next step
        if "next" not in state:
            state["next"] = "initial_contact"
        
        # Initialize flags
        state["can_proceed"] = True
        state["requires_immediate_attention"] = False
        
        logger.info(f"Intake agent initialized in {state['intake_mode']} mode")
        
        return state
    
    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the intake agent workflow.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info(f"Executing intake agent, current step: {state.get('current_step', 'unknown')}")
        
        # Get the current step
        current_step = state.get("current_step", "initial_contact")
        
        # Route to appropriate node based on current step
        if current_step == "initial_contact":
            state = await self.nodes.initial_contact(state, config)
        elif current_step == "collect_personal_info":
            state = await self.nodes.collect_personal_info(state, config)
        elif current_step == "collect_case_details":
            state = await self.nodes.collect_case_details(state, config)
        elif current_step == "practice_area_details":
            state = await self.nodes.practice_area_details(state, config)
        elif current_step == "check_conflicts":
            state = await self.nodes.check_conflicts(state, config)
        elif current_step == "summarize_and_confirm":
            state = await self.nodes.summarize_and_confirm(state, config)
        elif current_step == "save_client_info":
            state = await self.nodes.save_client_info(state, config)
        else:
            logger.warning(f"Unknown step: {current_step}")
            state["next"] = "FINISH"
        
        # Handle urgent cases
        if state.get("requires_immediate_attention"):
            await self._handle_urgent_case(state, config)
        
        # Validate state before proceeding
        await self._validate_state(state)
        
        logger.info(f"Intake agent execution completed, next: {state.get('next', 'unknown')}")
        
        return state
    
    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Cleanup after intake agent execution.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Cleaning up intake agent")
        
        # Log completion metrics
        completed_steps = state.get("completed_steps", [])
        practice_area = state.get("matter", {}).get("practice_area", "unknown")
        urgency = state.get("matter", {}).get("urgency", "low")
        
        logger.info(
            f"Intake completed: {len(completed_steps)} steps, "
            f"practice_area={practice_area}, urgency={urgency}"
        )
        
        # Set final status
        if state.get("can_proceed", True):
            state["status"] = "completed"
        else:
            state["status"] = "rejected"
        
        return state
    
    async def _handle_urgent_case(self, state: Dict[str, Any], config: RunnableConfig) -> None:
        """Handle urgent cases with special processing."""
        logger.info("Handling urgent case")
        
        urgency = state.get("matter", {}).get("urgency", "low")
        practice_area = state.get("matter", {}).get("practice_area")
        
        # Add urgent case notifications
        if urgency == "critical":
            # For critical cases, add immediate attention message
            urgent_message = "⚠️ URGENT: This case requires immediate attention. Our team will be notified immediately."
            
            if practice_area == PracticeArea.CRIMINAL_DEFENSE.value:
                urgent_message += " Criminal cases with imminent court dates are our highest priority."
            
            state["messages"].append({
                "type": "system",
                "content": urgent_message
            })
        
        # TODO: Implement actual urgent case notifications (email, SMS, etc.)
        
    async def _validate_state(self, state: Dict[str, Any]) -> None:
        """Validate the current state and identify any issues."""
        validation_errors = []
        
        # Check required client information
        client = state.get("client", {})
        if not client.get("name"):
            validation_errors.append("Client name is required")
        if not client.get("email"):
            validation_errors.append("Client email is required")
        if not client.get("phone"):
            validation_errors.append("Client phone is required")
        
        # Check matter information
        matter = state.get("matter", {})
        if not matter.get("description"):
            validation_errors.append("Matter description is required")
        
        # Practice area specific validation
        practice_area = matter.get("practice_area")
        if practice_area and practice_area in [pa.value for pa in PracticeArea]:
            practice_area_enum = PracticeArea(practice_area)
            if practice_area_enum in self.practice_area_configs:
                config = self.practice_area_configs[practice_area_enum]
                display_label = matter.get("display_label", "matters")
                for field in config["required_fields"]:
                    if not matter.get(field):
                        validation_errors.append(f"{field.replace('_', ' ').title()} is required for {practice_area.replace('_', ' ').title()} {display_label.lower()}")
        
        # Store validation errors
        state["validation_errors"] = validation_errors
        
        if validation_errors:
            logger.warning(f"State validation errors: {validation_errors}")
    
    def get_supported_practice_areas(self) -> List[str]:
        """Get list of supported practice areas."""
        return [pa.value for pa in PracticeArea]
    
    def get_workflow_steps(self) -> List[str]:
        """Get list of workflow steps."""
        return self.workflow_steps.copy()
    
    async def classify_matter_from_description(self, description: str, additional_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Intelligently classify a matter from description using LLM reasoning.

        Args:
            description: Matter description
            additional_context: Additional context for classification

        Returns:
            Enhanced classification result with LLM insights
        """
        try:
            # Use intelligent LLM-powered classifier
            result = await self.intelligent_classifier.classify_matter(description, additional_context)

            return {
                "practice_area": result.practice_area.value,
                "work_type": result.work_type.value,
                "case_type": result.case_type,
                "urgency": result.urgency.value,
                "confidence": result.confidence,
                "reasoning": result.reasoning,
                "keywords_matched": result.keywords_matched,
                "display_label": result.display_label,
                # Enhanced LLM insights
                "key_factors": result.key_factors,
                "timeline_factors": result.timeline_factors,
                "parties_involved": result.parties_involved,
                "potential_damages": result.potential_damages,
                "complexity_indicators": result.complexity_indicators,
                "red_flags": result.red_flags,
                "llm_reasoning": result.llm_reasoning,
                "fallback_used": result.fallback_used
            }

        except Exception as e:
            logger.error(f"Error in intelligent classification: {str(e)}")

            # Fallback to simple classifier
            result = self.classifier.classify_case(description)

            return {
                "practice_area": result.practice_area.value,
                "work_type": result.work_type.value,
                "case_type": result.case_type,
                "urgency": result.urgency.value,
                "confidence": result.confidence,
                "reasoning": result.reasoning,
                "keywords_matched": result.keywords_matched,
                "display_label": result.display_label,
                "key_factors": ["Fallback classification"],
                "timeline_factors": [],
                "parties_involved": [],
                "potential_damages": [],
                "complexity_indicators": [],
                "red_flags": [],
                "llm_reasoning": "Fallback to keyword classification due to error",
                "fallback_used": True
            }
