#!/usr/bin/env python3
"""
Enhanced Master Router Demo

This demo showcases the enhanced master router with support for document and deadline agents.
It demonstrates how the router correctly identifies different types of user intents and routes
them to the appropriate specialized agents.

Usage:
    python enhanced_master_router_demo.py
"""

import asyncio
import logging
from typing import Dict, Any

from langchain_core.messages import HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the enhanced master router functions directly to avoid dependency issues
from backend.agents.interactive.master_router import (
    _is_calendar_intent,
    _is_task_intent,
    _is_document_intent,
    _is_deadline_intent
)


def demo_intent_detection(user_input: str, description: str) -> None:
    """
    Demo intent detection for a single input.

    Args:
        user_input: The user input to test
        description: Description of what this test demonstrates
    """
    print(f"\n{'='*60}")
    print(f"Demo: {description}")
    print(f"Input: '{user_input}'")
    print(f"{'='*60}")

    # Test all intent detection functions
    intents = []

    if _is_calendar_intent(user_input):
        intents.append("calendar_graph")

    if _is_task_intent(user_input):
        intents.append("task_graph")

    if _is_document_intent(user_input):
        intents.append("document_agent")

    if _is_deadline_intent(user_input):
        intents.append("deadline_agent")

    # Check for explicit namespaces
    user_lower = user_input.lower()
    if user_lower.startswith("matter.") or user_lower.startswith("client.") or user_lower.startswith("case."):
        intents.append("matter_client_agent")

    # Check for research keywords
    research_keywords = ["research", "find", "search", "lookup", "legal", "case law", "statute"]
    if any(keyword in user_lower for keyword in research_keywords) and "document" not in user_lower and "deadline" not in user_lower:
        intents.append("research_agent")

    # Check for intake keywords
    intake_keywords = ["new client", "intake", "new case", "new matter", "client information"]
    if any(keyword in user_lower for keyword in intake_keywords):
        intents.append("intake_agent")

    # Determine primary routing (explicit namespaces take priority)
    primary_agent = None
    if user_lower.startswith(("matter.", "client.", "case.", "task.", "document.", "deadline.", "calendar.")):
        if user_lower.startswith(("matter.", "client.", "case.")):
            primary_agent = "matter_client_agent"
        elif user_lower.startswith("task."):
            primary_agent = "task_graph"
        elif user_lower.startswith("document."):
            primary_agent = "document_agent"
        elif user_lower.startswith("deadline."):
            primary_agent = "deadline_agent"
        elif user_lower.startswith("calendar."):
            primary_agent = "calendar_graph"
    elif intents:
        primary_agent = intents[0]  # First detected intent
    else:
        primary_agent = "supervisor_agent"

    print(f"✅ Primary Route: {primary_agent}")

    if intents:
        print(f"🎯 Detected Intents: {', '.join(intents)}")
    else:
        print("🎯 No specific intents detected (would route to supervisor)")

    # Provide context about what this agent does
    agent_descriptions = {
        "calendar_graph": "Calendar management (create, read, update, delete events)",
        "task_graph": "Task management operations",
        "document_agent": "Legal document generation and management",
        "deadline_agent": "Deadline tracking and calculation",
        "research_agent": "Legal research queries",
        "matter_client_agent": "Case and client management",
        "intake_agent": "New client intake processes",
        "supervisor_agent": "Complex routing decisions and fallback"
    }

    description = agent_descriptions.get(primary_agent, "Unknown agent")
    print(f"📋 Agent Purpose: {description}")


def main():
    """Run the enhanced master router demo."""
    print("🚀 Enhanced Master Router Demo")
    print("Demonstrating routing to document and deadline agents")

    # Test cases for different agent types
    test_cases = [
        # Calendar intents
        ("Schedule a meeting with John tomorrow at 2pm", "Calendar Intent - Meeting Scheduling"),
        ("Show me my calendar for next week", "Calendar Intent - Calendar Viewing"),
        ("calendar.create new event", "Calendar Intent - Explicit Namespace"),

        # Task intents
        ("Create a new task for reviewing contracts", "Task Intent - Task Creation"),
        ("Show me all my pending tasks", "Task Intent - Task Viewing"),
        ("task.update status to completed", "Task Intent - Explicit Namespace"),

        # Document intents (NEW!)
        ("Draft a demand letter for my client", "Document Intent - Document Creation"),
        ("Generate a settlement agreement", "Document Intent - Legal Document"),
        ("Write a legal document for the case", "Document Intent - General Document"),
        ("Edit document content", "Document Intent - Document Editing"),
        ("document.create new contract", "Document Intent - Explicit Namespace"),

        # Deadline intents (NEW!)
        ("Check the statute of limitations for this case", "Deadline Intent - Statute Check"),
        ("What are the upcoming deadlines?", "Deadline Intent - Deadline Tracking"),
        ("Calculate filing deadline", "Deadline Intent - Deadline Calculation"),
        ("When is the discovery cutoff?", "Deadline Intent - Legal Deadline"),
        ("deadline.track court dates", "Deadline Intent - Explicit Namespace"),

        # Research intents
        ("Research personal injury law precedents", "Research Intent - Legal Research"),
        ("Find case law about negligence", "Research Intent - Case Law Search"),

        # Matter/Client intents
        ("case.create new personal injury case", "Matter Intent - Case Creation"),
        ("Update client information", "Matter Intent - Client Management"),

        # Intake intents
        ("New client wants to discuss a case", "Intake Intent - New Client"),
        ("Client intake for personal injury", "Intake Intent - Intake Process"),

        # Fallback cases
        ("Hello, how are you?", "Supervisor Intent - General Greeting"),
        ("What can you help me with?", "Supervisor Intent - General Question"),
    ]

    # Run all test cases
    for user_input, description in test_cases:
        demo_intent_detection(user_input, description)

    print(f"\n{'='*60}")
    print("✅ Demo completed successfully!")
    print("📊 Summary:")
    print("  - Document Agent: Handles legal document generation and management")
    print("  - Deadline Agent: Manages deadline tracking and calculations")
    print("  - Enhanced routing with explicit namespace support")
    print("  - Comprehensive intent detection with keyword and pattern matching")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
