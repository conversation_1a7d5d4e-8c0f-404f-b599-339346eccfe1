"""
Master Router for Interactive Agents

This module provides the master router for the AiLex interactive agents system.
The master router analyzes user input and routes requests to the appropriate
specialized agent or graph based on intent detection.

The router supports routing to:
- Calendar CRUD Agent: For calendar-related operations (create, read, update,
  delete events)
  * Detects calendar intents using keywords and patterns
  * Routes to calendar_graph for comprehensive calendar management
  * Supports natural language calendar operations
- Task CRUD Agent: For task management operations
- Case & Client CRUD Agent: For case and client management
- Research Agent: For legal research queries
- Intake Agent: For new client intake processes
- Document Agent: For legal document generation and management
  * Detects document intents for drafting, editing, and reviewing
  * Supports demand letters, settlement agreements, court filings
  * Routes to document_agent for document operations
- Deadline Agent: For deadline tracking and calculation
  * Detects deadline intents for statute of limitations, court deadlines
  * Supports deadline calculations, notifications, and tracking
  * Routes to deadline_agent for deadline management

Calendar Intent Detection:
The router uses sophisticated intent detection for calendar operations, including:
- Schedule/booking keywords: "schedule", "book", "create meeting", "set appointment"
- View/read keywords: "show calendar", "view calendar", "my schedule"
- Update keywords: "reschedule", "move meeting", "change appointment"
- Delete keywords: "cancel meeting", "delete event", "remove appointment"
- Availability keywords: "when am i free", "available time", "find time"
- Explicit namespace: "calendar.create", "calendar.list", etc.

Usage:
    from backend.agents.interactive.master_router import (
        master_router, create_master_graph
    )

    # Use in a LangGraph StateGraph
    sg.add_node("masterRouter", master_router)

    # Or create a complete master graph
    graph = create_master_graph(voyage=voyage_client)

    # Example routing scenarios:
    # "Schedule a meeting tomorrow" → calendar_graph
    # "matter.create new matter" → matter_client_agent
    # "Research personal injury law" → research_agent
    # "Draft a demand letter" → document_agent
    # "Check statute of limitations" → deadline_agent
"""

import asyncio
import json
import logging
import re
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Literal, Optional, TypedDict

from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.calendar_crud.graph import create_calendar_graph
from backend.agents.interactive.task_crud.graph import create_task_graph
from shared.core.llm.voyage import VoyageClient

# Set up logging
logger = logging.getLogger(__name__)


# Data classes for parallel detection system
@dataclass
class RoutingResult:
    """Result from a single routing detection method."""
    agent: str
    confidence: float
    method: Literal["llm", "keyword", "consensus", "fallback", "explicit"]
    reasoning: str
    response_time: float


@dataclass
class KeywordDetectionResult:
    """Result from keyword detection with detailed scoring."""
    agent: str
    confidence_score: float  # 0.0 to 1.0
    matched_patterns: List[str]
    pattern_weights: Dict[str, float]


@dataclass
class ParallelDetectionResult:
    """Result from parallel LLM + keyword detection."""
    llm_result: Optional[RoutingResult]
    keyword_result: RoutingResult
    consensus_result: RoutingResult
    total_time: float


# Define the router output type
class MasterRouterOutput(TypedDict):
    next: Literal[
        "calendar_graph",
        "task_graph",
        "matter_client_agent",
        "research_agent",
        "intake_agent",
        "document_agent",
        "deadline_agent",
        "supervisor_agent"
    ]

# Calendar intent keywords for routing
CALENDAR_KEYWORDS = [
    # Event creation
    "schedule", "book", "create meeting", "set appointment", "add event",
    "plan meeting", "arrange", "organize meeting",
    
    # Event reading/viewing
    "show calendar", "view calendar", "check calendar", "my schedule",
    "what's on my calendar", "calendar events", "upcoming meetings",
    "today's schedule", "tomorrow's schedule", "this week's schedule",
    
    # Event updating
    "reschedule", "move meeting", "change appointment", "update event",
    "modify meeting", "edit appointment",
    
    # Event deletion
    "cancel meeting", "delete event", "remove appointment", "cancel appointment",
    
    # Free/busy checking
    "when am i free", "available time", "free time", "busy time",
    "check availability", "find time", "when can we meet"
]

# Calendar intent patterns (regex)
CALENDAR_PATTERNS = [
    r"calendar\.",  # Explicit calendar namespace
    r"schedule.*(?:meeting|appointment|event)",
    r"(?:create|add|book).*(?:meeting|appointment|event)",
    r"(?:show|view|check).*calendar",
    r"(?:reschedule|move|change).*(?:meeting|appointment)",
    r"(?:cancel|delete|remove).*(?:meeting|appointment|event)",
    r"when.*(?:free|available|busy)",
    r"find.*time.*(?:meeting|meet)"
]


async def master_router(state: Dict[str, Any],
                       config: RunnableConfig) -> MasterRouterOutput:
    """
    Master router node for interactive agents.
    
    This node analyzes user input and determines which specialized agent
    or graph should handle the request based on intent detection.
    
    Args:
        state: Agent state containing messages and context
        config: Runnable configuration
        
    Returns:
        MasterRouterOutput: Next agent/graph to execute
    """
    logger.info("Master router analyzing user input for intent detection")
    
    # Get the user input from messages
    messages = state.get("messages", [])
    if not messages:
        logger.info("No messages found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if (isinstance(message, HumanMessage) or
            (hasattr(message, "type") and message.type == "human")):
            user_input = (message.content if hasattr(message, 'content')
                         else str(message))
            break
        elif isinstance(message, dict) and message.get("type") == "human":
            user_input = message.get("content", "")
            break
    
    if not user_input:
        logger.info("No user input found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    logger.info(f"Analyzing user input: {user_input[:100]}...")
    
    # Check for calendar intent
    if _is_calendar_intent(user_input):
        logger.info("Calendar intent detected, routing to calendar_graph")
        return {"next": "calendar_graph"}
    
    # Check for explicit agent routing (e.g., "case.create", "task.list")
    # Explicit namespaces take priority over keyword detection
    if (user_input.lower().startswith("matter.") or
        user_input.lower().startswith("client.") or
        user_input.lower().startswith("case.")):
        logger.info("Matter/Client intent detected, routing to matter_client_agent")
        return {"next": "matter_client_agent"}

    # Check for explicit task namespace first
    if user_input.lower().startswith("task."):
        logger.info("Task intent detected (explicit namespace), routing to task_graph")
        return {"next": "task_graph"}

    # Check for explicit document namespace first
    if user_input.lower().startswith("document."):
        logger.info("Document intent detected (explicit namespace), routing to document_agent")
        return {"next": "document_agent"}

    # Check for explicit deadline namespace first
    if user_input.lower().startswith("deadline."):
        logger.info("Deadline intent detected (explicit namespace), routing to deadline_agent")
        return {"next": "deadline_agent"}

    # Now check for keyword-based intents (less priority than explicit namespaces)
    # Check deadline intent first as it's more specific than document intent
    if _is_deadline_intent(user_input):
        logger.info("Deadline intent detected, routing to deadline_agent")
        return {"next": "deadline_agent"}

    if _is_document_intent(user_input):
        logger.info("Document intent detected, routing to document_agent")
        return {"next": "document_agent"}

    if _is_task_intent(user_input):
        logger.info("Task intent detected, routing to task_graph")
        return {"next": "task_graph"}

    # Check for research keywords
    research_keywords = ["research", "find", "search", "lookup", "legal",
                        "case law", "statute"]
    if any(keyword in user_input.lower() for keyword in research_keywords):
        logger.info("Research intent detected, routing to research_agent")
        return {"next": "research_agent"}

    # Check for intake keywords (for new matters)
    intake_keywords = ["new client", "intake", "new case", "new matter",
                      "client information"]
    if any(keyword in user_input.lower() for keyword in intake_keywords):
        logger.info("Intake intent detected, routing to intake_agent")
        return {"next": "intake_agent"}

    # Default to supervisor agent for complex routing decisions
    logger.info("No specific intent detected, routing to supervisor_agent")
    return {"next": "supervisor_agent"}


def _is_calendar_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates calendar-related intent.
    
    Args:
        user_input: The user's input text
        
    Returns:
        bool: True if calendar intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()
    
    # Check for explicit calendar keywords
    for keyword in CALENDAR_KEYWORDS:
        if keyword in user_input_lower:
            logger.debug(f"Calendar keyword '{keyword}' found in user input")
            return True
    
    # Check for calendar patterns using regex
    for pattern in CALENDAR_PATTERNS:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Calendar pattern '{pattern}' matched in user input")
            return True
    
    return False


def _is_task_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates task-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if task intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Task intent keywords for routing
    task_keywords = [
        # Task creation
        "create task", "add task", "new task", "make task", "schedule task",

        # Task reading/viewing
        "show tasks", "view tasks", "list tasks", "get tasks", "my tasks",
        "find task", "search task", "retrieve task", "search for task",

        # Task updating
        "update task", "change task", "modify task", "edit task",
        "mark task", "complete task", "finish task",

        # Task deletion
        "delete task", "remove task", "cancel task"
    ]

    # Check for task keywords
    for keyword in task_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Task keyword '{keyword}' found in user input")
            return True

    # Task intent patterns (regex)
    task_patterns = [
        r"(?:create|add|make|new).*task",
        r"(?:show|view|list|get).*tasks?",
        r"(?:update|change|modify|edit).*task",
        r"(?:delete|remove|cancel).*task",
        r"mark.*task.*(?:done|complete|finished)",
        r"task.*(?:create|add|update|delete|list|show)"
    ]

    # Check for task patterns using regex
    for pattern in task_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Task pattern '{pattern}' matched in user input")
            return True

    return False


def _enhanced_keyword_detection(user_input: str) -> KeywordDetectionResult:
    """Enhanced keyword detection with weighted confidence scoring."""

    # High-confidence patterns (weight: 0.85-0.95)
    high_confidence_patterns = {
        "document_agent": [
            (r"draft\s+(demand\s+letter|settlement\s+agreement)", 0.95),
            (r"generate\s+(contract|agreement|motion)", 0.90),
            (r"write\s+(legal\s+document|brief|pleading)", 0.90),
            (r"create\s+(document|contract|agreement)", 0.85),
        ],
        "deadline_agent": [
            (r"statute\s+of\s+limitations", 0.95),
            (r"calculate\s+deadline", 0.90),
            (r"when\s+is\s+.+\s+due", 0.85),
            (r"track\s+deadline", 0.90),
            (r"filing\s+deadline", 0.90),
        ],
        "calendar_graph": [
            (r"schedule\s+(meeting|appointment|deposition)", 0.95),
            (r"book\s+(time|appointment)", 0.90),
            (r"calendar\s+(event|meeting)", 0.85),
        ],
        "task_graph": [
            (r"create\s+(task|todo)", 0.90),
            (r"add\s+task", 0.85),
            (r"mark\s+task\s+(complete|done)", 0.90),
        ]
    }

    # Medium-confidence patterns (weight: 0.6-0.8)
    medium_confidence_patterns = {
        "document_agent": [
            (r"document", 0.6),
            (r"contract", 0.7),
            (r"agreement", 0.6),
            (r"draft", 0.65),
        ],
        "deadline_agent": [
            (r"deadline", 0.7),
            (r"due\s+date", 0.8),
            (r"filing\s+date", 0.8),
            (r"statute", 0.75),
        ],
        "calendar_graph": [
            (r"schedule", 0.7),
            (r"meeting", 0.6),
            (r"appointment", 0.7),
        ],
        "task_graph": [
            (r"task", 0.6),
            (r"todo", 0.65),
        ]
    }

    user_lower = user_input.lower()
    agent_scores = {}
    matched_patterns = {}
    pattern_weights = {}

    # Score high-confidence patterns first
    for agent, patterns in high_confidence_patterns.items():
        score = 0.0
        matches = []
        weights = {}
        for pattern, weight in patterns:
            if re.search(pattern, user_lower):
                score = max(score, weight)
                matches.append(pattern)
                weights[pattern] = weight
        if score > 0:
            agent_scores[agent] = score
            matched_patterns[agent] = matches
            pattern_weights[agent] = weights

    # If no high-confidence matches, check medium-confidence
    if not agent_scores:
        for agent, patterns in medium_confidence_patterns.items():
            score = 0.0
            matches = []
            weights = {}
            for pattern, weight in patterns:
                if re.search(pattern, user_lower):
                    score = max(score, weight)
                    matches.append(pattern)
                    weights[pattern] = weight
            if score > 0:
                agent_scores[agent] = score
                matched_patterns[agent] = matches
                pattern_weights[agent] = weights

    # Return best match or supervisor
    if agent_scores:
        best_agent = max(agent_scores.keys(), key=lambda k: agent_scores[k])
        return KeywordDetectionResult(
            agent=best_agent,
            confidence_score=agent_scores[best_agent],
            matched_patterns=matched_patterns[best_agent],
            pattern_weights=pattern_weights[best_agent]
        )
    else:
        return KeywordDetectionResult(
            agent="supervisor_agent",
            confidence_score=0.0,
            matched_patterns=[],
            pattern_weights={}
        )


def _is_document_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates document-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if document intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Document intent keywords for routing
    document_keywords = [
        # Document creation (specific phrases)
        "create document", "generate document", "draft document", "write document",
        "new document", "make document", "compose document",

        # Document types (specific legal documents)
        "demand letter", "settlement agreement", "court filing", "legal brief",
        "contract", "agreement", "motion", "pleading", "complaint", "answer",
        "discovery request", "deposition notice", "subpoena",

        # Document actions (specific to documents)
        "draft letter", "draft agreement", "draft contract", "draft motion",
        "write letter", "write agreement", "write contract", "write brief", "write document",
        "compose letter", "compose agreement", "compose motion",
        "generate letter", "generate agreement", "generate contract",
        "edit document", "update document", "revise document", "modify document",
        "review document", "check document", "proofread document", "check the document",

        # Document management
        "document template", "legal template", "legal form"
    ]

    # Check for document keywords
    for keyword in document_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Document keyword '{keyword}' found in user input")
            return True

    # Document intent patterns (regex)
    document_patterns = [
        r"document\.",  # Explicit document namespace
        r"(?:draft|write|compose|generate).*(?:letter|agreement|contract|motion|brief|filing|pleading|document)",
        r"(?:demand|settlement|court|legal).*(?:letter|agreement|filing|brief|document)",
        r"(?:edit|update|revise|modify).*(?:document|letter|agreement|contract)",
        r"(?:create|generate|make).*(?:document|letter|agreement|contract|motion|brief)",
        r"document.*(?:template|form)",
        r"legal.*document"
    ]

    # Check for document patterns using regex
    for pattern in document_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Document pattern '{pattern}' matched in user input")
            return True

    return False


def _is_deadline_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates deadline-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if deadline intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Deadline intent keywords for routing
    deadline_keywords = [
        # Deadline tracking
        "deadline", "due date", "statute of limitations", "filing deadline",
        "court deadline", "discovery deadline", "response deadline",

        # Deadline actions
        "check deadlines", "upcoming deadlines", "deadline reminder",
        "calculate deadline", "deadline calculation", "when is due",
        "deadline tracker", "track deadline", "monitor deadline",

        # Legal deadlines
        "statute", "limitation period", "time limit", "filing period",
        "response time", "discovery cutoff", "trial date", "hearing date",
        "motion deadline", "appeal deadline", "settlement deadline",

        # Deadline management
        "deadline alert", "deadline notification", "deadline warning",
        "missed deadline", "approaching deadline", "overdue"
    ]

    # Check for deadline keywords
    for keyword in deadline_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Deadline keyword '{keyword}' found in user input")
            return True

    # Deadline intent patterns (regex)
    deadline_patterns = [
        r"deadline\.",  # Explicit deadline namespace
        r"(?:check|show|view|list|get).*deadline",
        r"(?:calculate|compute|determine).*(?:deadline|due.*date|statute)",
        r"(?:statute|limitation).*(?:period|deadline|date)",
        r"(?:filing|court|discovery|response).*(?:deadline|due|date)",
        r"deadline.*(?:check|calculate|track|monitor|alert|reminder)",
        r"when.*(?:due|deadline|expires|statute)",
        r"(?:upcoming|approaching|missed|overdue).*deadline"
    ]

    # Check for deadline patterns using regex
    for pattern in deadline_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Deadline pattern '{pattern}' matched in user input")
            return True

    return False


async def _safe_llm_detection(user_input: str, config: RunnableConfig) -> Optional[RoutingResult]:
    """Safe LLM detection with timeout and error handling."""
    start_time = time.time()

    try:
        # Get LLM client from config (using existing voyage client pattern)
        voyage_client = config.get("configurable", {}).get("voyage_client")
        if not voyage_client:
            logger.warning("No voyage client available for LLM detection")
            return None

        prompt = f"""
        Analyze this user request and determine the best agent to handle it.

        USER REQUEST: "{user_input}"

        Available agents:
        - document_agent: Legal document creation/editing (contracts, letters, motions, briefs, demand letters, settlement agreements)
        - deadline_agent: Legal deadline tracking/calculation (statutes of limitations, court dates, filing deadlines, discovery deadlines)
        - calendar_graph: Scheduling and calendar management (meetings, appointments, events)
        - task_graph: Task creation and management (create tasks, mark complete, list tasks)
        - matter_client_agent: Case and client data management (case creation, client information updates)
        - research_agent: Legal research and case law lookup (find precedents, research statutes)
        - intake_agent: New client onboarding (initial consultations, client intake forms)
        - supervisor_agent: General assistance or unclear requests

        Focus on the PRIMARY action the user wants to take. For example:
        - "Track deadline for motion filing" -> deadline_agent (tracking is the primary action)
        - "Draft motion for deadline extension" -> document_agent (drafting is the primary action)

        Respond with JSON only:
        {{
            "agent": "agent_name",
            "confidence": 0.85,
            "reasoning": "Brief explanation of choice"
        }}
        """

        # Use the voyage client to generate response
        response = await voyage_client.generate_text(prompt)

        # Parse JSON response
        try:
            result_data = json.loads(response.strip())
        except json.JSONDecodeError:
            # Try to extract JSON from response if it's wrapped in other text
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result_data = json.loads(json_match.group())
            else:
                logger.warning(f"Could not parse LLM response as JSON: {response}")
                return None

        response_time = time.time() - start_time

        # Validate agent name
        valid_agents = {
            "document_agent", "deadline_agent", "calendar_graph", "task_graph",
            "matter_client_agent", "research_agent", "intake_agent", "supervisor_agent"
        }

        agent = result_data.get("agent", "supervisor_agent")
        if agent not in valid_agents:
            logger.warning(f"LLM returned invalid agent: {agent}, defaulting to supervisor")
            agent = "supervisor_agent"

        return RoutingResult(
            agent=agent,
            confidence=float(result_data.get("confidence", 0.5)),
            method="llm",
            reasoning=result_data.get("reasoning", "LLM analysis"),
            response_time=response_time
        )

    except Exception as e:
        logger.warning(f"LLM detection failed: {e}")
        return None


async def _keyword_detection_async(user_input: str) -> RoutingResult:
    """Async wrapper for keyword detection (for parallel execution)."""
    start_time = time.time()

    # Use enhanced keyword detection with confidence scoring
    result = _enhanced_keyword_detection(user_input)

    response_time = time.time() - start_time

    return RoutingResult(
        agent=result.agent,
        confidence=result.confidence_score,
        method="keyword",
        reasoning=f"Matched patterns: {', '.join(result.matched_patterns) if result.matched_patterns else 'no patterns'}",
        response_time=response_time
    )


def _generate_consensus(
    llm_result: Optional[RoutingResult],
    keyword_result: RoutingResult,
    user_input: str
) -> RoutingResult:
    """
    Generate consensus routing decision from parallel detection results.

    Consensus Logic:
    1. If LLM failed -> use keyword result
    2. If both agree -> boost confidence (consensus)
    3. If both disagree -> use confidence-based decision
    4. Special handling for high-confidence disagreements
    """

    # Case 1: LLM failed or unavailable
    if llm_result is None:
        return RoutingResult(
            agent=keyword_result.agent,
            confidence=keyword_result.confidence,
            method="fallback",
            reasoning=f"LLM unavailable, using keyword result: {keyword_result.reasoning}",
            response_time=keyword_result.response_time
        )

    # Case 2: Perfect agreement - boost confidence
    if llm_result.agent == keyword_result.agent:
        boosted_confidence = min(0.95, (llm_result.confidence + keyword_result.confidence) / 2 + 0.15)
        return RoutingResult(
            agent=llm_result.agent,
            confidence=boosted_confidence,
            method="consensus",
            reasoning=f"LLM and keyword agree: {llm_result.reasoning}",
            response_time=max(llm_result.response_time, keyword_result.response_time)
        )

    # Case 3: Disagreement - use confidence-based decision with special rules
    return _resolve_disagreement(llm_result, keyword_result, user_input)


def _resolve_disagreement(
    llm_result: RoutingResult,
    keyword_result: RoutingResult,
    user_input: str
) -> RoutingResult:
    """
    Resolve disagreements between LLM and keyword detection.

    Resolution Strategy:
    1. High confidence wins (>0.8)
    2. LLM wins for complex cases (>0.7)
    3. Keyword wins for simple/obvious cases
    4. Supervisor for unclear cases
    """

    # High confidence keyword beats medium confidence LLM
    if keyword_result.confidence >= 0.85 and llm_result.confidence < 0.75:
        return RoutingResult(
            agent=keyword_result.agent,
            confidence=keyword_result.confidence * 0.9,  # Slight penalty for disagreement
            method="keyword",
            reasoning=f"High-confidence keyword overrides LLM: {keyword_result.reasoning}",
            response_time=keyword_result.response_time
        )

    # High confidence LLM beats medium confidence keyword
    if llm_result.confidence >= 0.8 and keyword_result.confidence < 0.7:
        return RoutingResult(
            agent=llm_result.agent,
            confidence=llm_result.confidence * 0.9,  # Slight penalty for disagreement
            method="llm",
            reasoning=f"High-confidence LLM overrides keyword: {llm_result.reasoning}",
            response_time=llm_result.response_time
        )

    # Special case: deadline vs document conflict (common edge case)
    if _is_deadline_document_conflict(llm_result.agent, keyword_result.agent):
        return _resolve_deadline_document_conflict(llm_result, keyword_result, user_input)

    # Both medium confidence - prefer LLM for complex understanding
    if llm_result.confidence >= 0.65:
        return RoutingResult(
            agent=llm_result.agent,
            confidence=llm_result.confidence * 0.8,  # Penalty for disagreement
            method="llm",
            reasoning=f"LLM chosen over keyword disagreement: {llm_result.reasoning}",
            response_time=llm_result.response_time
        )

    # Low confidence all around - use supervisor
    return RoutingResult(
        agent="supervisor_agent",
        confidence=0.5,
        method="fallback",
        reasoning="Low confidence from both LLM and keyword detection",
        response_time=max(llm_result.response_time, keyword_result.response_time)
    )


def _is_deadline_document_conflict(agent1: str, agent2: str) -> bool:
    """Check if this is a deadline vs document conflict."""
    return {agent1, agent2} == {"deadline_agent", "document_agent"}


def _resolve_deadline_document_conflict(
    llm_result: RoutingResult,
    keyword_result: RoutingResult,
    user_input: str
) -> RoutingResult:
    """
    Resolve deadline vs document conflicts with domain-specific logic.

    Examples:
    - "Track deadline for motion filing" -> deadline_agent (tracking is key)
    - "Draft motion for deadline extension" -> document_agent (drafting is key)
    """

    # Deadline action words take priority
    deadline_actions = ["track", "check", "calculate", "monitor", "when", "due", "expires"]
    document_actions = ["draft", "write", "create", "generate", "edit", "review"]

    user_lower = user_input.lower()

    # Count action words
    deadline_score = sum(1 for action in deadline_actions if action in user_lower)
    document_score = sum(1 for action in document_actions if action in user_lower)

    if deadline_score > document_score:
        # Prefer deadline agent
        if llm_result.agent == "deadline_agent":
            return llm_result
        else:
            return keyword_result
    elif document_score > deadline_score:
        # Prefer document agent
        if llm_result.agent == "document_agent":
            return llm_result
        else:
            return keyword_result
    else:
        # Tie - use higher confidence
        return llm_result if llm_result.confidence >= keyword_result.confidence else keyword_result


async def _parallel_intent_detection(user_input: str, config: RunnableConfig) -> ParallelDetectionResult:
    """Execute LLM and keyword detection in parallel with consensus logic."""
    start_time = time.time()

    # Create parallel tasks
    llm_task = _safe_llm_detection(user_input, config)
    keyword_task = _keyword_detection_async(user_input)

    # Execute in parallel with timeout
    try:
        results = await asyncio.wait_for(
            asyncio.gather(llm_task, keyword_task, return_exceptions=True),
            timeout=3.0  # 3 second timeout for LLM
        )

        llm_result, keyword_result = results

        # Handle LLM exceptions
        if isinstance(llm_result, Exception):
            logger.warning(f"LLM detection failed: {llm_result}")
            llm_result = None

        # Handle keyword exceptions (shouldn't happen, but safety first)
        if isinstance(keyword_result, Exception):
            logger.error(f"Keyword detection failed: {keyword_result}")
            keyword_result = RoutingResult(
                agent="supervisor_agent",
                confidence=0.0,
                method="fallback",
                reasoning="Keyword detection failed",
                response_time=0.0
            )

    except asyncio.TimeoutError:
        logger.warning("LLM detection timed out, using keyword result only")
        llm_result = None
        keyword_result = await keyword_task

    # Generate consensus result
    consensus_result = _generate_consensus(llm_result, keyword_result, user_input)

    total_time = time.time() - start_time

    return ParallelDetectionResult(
        llm_result=llm_result,
        keyword_result=keyword_result,
        consensus_result=consensus_result,
        total_time=total_time
    )


def _check_explicit_namespace(user_input: str) -> Optional[str]:
    """Check for explicit namespace routing (e.g., 'case.create', 'document.draft')."""
    user_lower = user_input.lower()

    if user_lower.startswith(("matter.", "client.", "case.")):
        return "matter_client_agent"
    elif user_lower.startswith("task."):
        return "task_graph"
    elif user_lower.startswith("document."):
        return "document_agent"
    elif user_lower.startswith("deadline."):
        return "deadline_agent"
    elif user_lower.startswith("calendar."):
        return "calendar_graph"

    return None


async def enhanced_parallel_master_router(state: Dict[str, Any],
                                         config: RunnableConfig) -> MasterRouterOutput:
    """
    Enhanced parallel master router with LLM + keyword consensus.

    This router uses parallel LLM and keyword detection with consensus validation
    for more accurate and reliable intent detection.

    Strategy:
    1. Explicit namespaces (highest priority, instant)
    2. Parallel LLM + keyword detection
    3. Consensus validation and conflict resolution
    4. Graceful fallback on LLM failure

    Args:
        state: Agent state containing messages and context
        config: Runnable configuration with voyage_client

    Returns:
        MasterRouterOutput: Next agent/graph to execute
    """
    start_time = time.time()
    logger.info("Enhanced parallel master router analyzing user input")

    # Extract user input from messages
    user_input = _extract_user_input(state)
    if not user_input:
        logger.info("No user input found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}

    logger.info(f"Analyzing user input: '{user_input[:100]}...'")

    # Phase 1: Explicit namespaces (instant, highest priority)
    explicit_agent = _check_explicit_namespace(user_input)
    if explicit_agent:
        logger.info(f"Explicit namespace routing: {explicit_agent}")
        return {"next": explicit_agent}

    # Phase 2: Parallel detection with consensus
    try:
        detection_result = await _parallel_intent_detection(user_input, config)

        # Log performance metrics
        total_time = time.time() - start_time
        logger.info(
            f"Parallel detection completed in {detection_result.total_time:.3f}s (total: {total_time:.3f}s): "
            f"LLM={detection_result.llm_result.agent if detection_result.llm_result else 'failed'}, "
            f"Keyword={detection_result.keyword_result.agent}, "
            f"Final={detection_result.consensus_result.agent} "
            f"(confidence: {detection_result.consensus_result.confidence:.2f}, "
            f"method: {detection_result.consensus_result.method})"
        )

        return {"next": detection_result.consensus_result.agent}

    except Exception as e:
        logger.error(f"Parallel detection failed: {e}, falling back to simple keyword detection")
        # Emergency fallback to simple keyword detection
        return await _emergency_fallback_router(user_input)


def _extract_user_input(state: Dict[str, Any]) -> str:
    """Extract user input from state messages."""
    messages = state.get("messages", [])
    if not messages:
        return ""

    # Find the last human message
    for message in reversed(messages):
        if (isinstance(message, HumanMessage) or
            (hasattr(message, "type") and message.type == "human")):
            return message.content if hasattr(message, "content") else str(message)
        elif isinstance(message, dict) and message.get("type") == "human":
            return message.get("content", "")

    return ""


async def _emergency_fallback_router(user_input: str) -> MasterRouterOutput:
    """Emergency fallback router using simple keyword detection."""
    logger.warning("Using emergency fallback routing")

    # Simple keyword-based routing as last resort
    user_lower = user_input.lower()

    # Check for obvious patterns
    if any(word in user_lower for word in ["schedule", "meeting", "calendar", "appointment"]):
        return {"next": "calendar_graph"}
    elif any(word in user_lower for word in ["task", "todo", "complete"]):
        return {"next": "task_graph"}
    elif any(word in user_lower for word in ["draft", "document", "contract", "letter"]):
        return {"next": "document_agent"}
    elif any(word in user_lower for word in ["deadline", "statute", "due"]):
        return {"next": "deadline_agent"}
    elif any(word in user_lower for word in ["research", "case law", "legal"]):
        return {"next": "research_agent"}
    elif any(word in user_lower for word in ["client", "case", "matter"]):
        return {"next": "matter_client_agent"}
    else:
        return {"next": "supervisor_agent"}


def create_master_graph(*, voyage: VoyageClient) -> Any:
    """
    Create the master graph that routes to specialized agents and graphs.

    This function builds a LangGraph StateGraph that implements the master routing
    workflow, connecting the master router to various specialized agents including
    the calendar graph.

    Args:
        voyage: VoyageClient instance for LLM operations

    Returns:
        StateGraph: Compiled master routing workflow graph
    """
    from langgraph.graph import END, StateGraph

    logger.info("Creating master routing StateGraph workflow")

    # Create the graph with Dict[str, Any] as the state type
    workflow = StateGraph(Dict[str, Any])

    # Add the enhanced parallel master router node
    async def master_router_with_voyage(state: Dict[str, Any], config: RunnableConfig) -> MasterRouterOutput:
        """Master router wrapper that adds voyage client to config."""
        # Add voyage client to config for LLM detection
        enhanced_config = config.copy() if config else {}
        if "configurable" not in enhanced_config:
            enhanced_config["configurable"] = {}
        enhanced_config["configurable"]["voyage_client"] = voyage

        return await enhanced_parallel_master_router(state, enhanced_config)

    workflow.add_node("master_router", master_router_with_voyage)

    # Create and add the calendar graph
    calendar_graph = create_calendar_graph(voyage=voyage)
    workflow.add_node("calendar_graph", calendar_graph)

    # Create and add the task graph
    task_graph = create_task_graph(voyage=voyage)
    workflow.add_node("task_graph", task_graph)

    # Add placeholder nodes for other agents (to be implemented)
    # These would be replaced with actual agent implementations
    async def placeholder_agent(state: Dict[str, Any],
                               config: RunnableConfig) -> Dict[str, Any]:
        """Placeholder agent implementation."""
        agent_name = state.get("_current_agent", "unknown")
        logger.info(f"Placeholder agent '{agent_name}' executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": (f"This is a placeholder response from {agent_name}. "
                       "Implementation pending.")
        }]
        return state

    # Document agent placeholder (will be replaced with actual implementation)
    async def document_agent_placeholder(state: Dict[str, Any],
                                       config: RunnableConfig) -> Dict[str, Any]:
        """Document agent placeholder implementation."""
        logger.info("Document agent placeholder executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": ("Document agent is ready to help with legal document generation. "
                       "I can assist with demand letters, settlement agreements, court filings, "
                       "and other legal documents. Full implementation coming soon.")
        }]
        return state

    # Deadline agent placeholder (will be replaced with actual implementation)
    async def deadline_agent_placeholder(state: Dict[str, Any],
                                       config: RunnableConfig) -> Dict[str, Any]:
        """Deadline agent placeholder implementation."""
        logger.info("Deadline agent placeholder executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": ("Deadline agent is ready to help with deadline tracking and calculations. "
                       "I can assist with statute of limitations, court deadlines, discovery deadlines, "
                       "and deadline notifications. Full implementation coming soon.")
        }]
        return state

    workflow.add_node("matter_client_agent", placeholder_agent)
    workflow.add_node("research_agent", placeholder_agent)
    workflow.add_node("intake_agent", placeholder_agent)
    workflow.add_node("document_agent", document_agent_placeholder)
    workflow.add_node("deadline_agent", deadline_agent_placeholder)
    workflow.add_node("supervisor_agent", placeholder_agent)

    # Set the entry point
    workflow.set_entry_point("master_router")

    # Add conditional edges from master router to specialized agents
    workflow.add_conditional_edges(
        "master_router",
        lambda state: state.get("next", "supervisor_agent"),
        {
            "calendar_graph": "calendar_graph",
            "task_graph": "task_graph",
            "matter_client_agent": "matter_client_agent",
            "research_agent": "research_agent",
            "intake_agent": "intake_agent",
            "document_agent": "document_agent",
            "deadline_agent": "deadline_agent",
            "supervisor_agent": "supervisor_agent",
        }
    )

    # Add edges from all agents to END
    workflow.add_edge("calendar_graph", END)
    workflow.add_edge("task_graph", END)
    workflow.add_edge("matter_client_agent", END)
    workflow.add_edge("research_agent", END)
    workflow.add_edge("intake_agent", END)
    workflow.add_edge("document_agent", END)
    workflow.add_edge("deadline_agent", END)
    workflow.add_edge("supervisor_agent", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()

    logger.info("Master routing StateGraph workflow created and compiled successfully")
    return compiled_workflow


def get_workflow_info() -> Dict[str, Any]:
    """
    Get information about the master routing workflow.

    Returns:
        Dict[str, Any]: Workflow information including nodes, edges, and capabilities
    """
    return {
        "name": "master_routing_workflow",
        "description": "LangGraph workflow for routing to specialized agents",
        "version": "1.0.0",
        "nodes": [
            "master_router",
            "calendar_graph",
            "task_graph",
            "matter_client_agent",
            "research_agent",
            "intake_agent",
            "document_agent",
            "deadline_agent",
            "supervisor_agent"
        ],
        "entry_point": "master_router",
        "capabilities": [
            "Intent detection and routing",
            "Calendar operations routing",
            "Task management routing",
            "Case and client management routing",
            "Legal research routing",
            "Client intake routing",
            "Document generation routing",
            "Deadline tracking routing",
            "Fallback to supervisor agent"
        ],
        "supported_intents": [
            "calendar.*",
            "task.*",
            "case.*",
            "client.*",
            "document.*",
            "deadline.*",
            "research queries",
            "intake processes"
        ]
    }
