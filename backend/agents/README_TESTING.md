# Agent System Testing Infrastructure

This document provides comprehensive documentation for the agent system testing infrastructure, including setup, usage, and best practices.

## Overview

The testing infrastructure provides a standardized, comprehensive framework for testing all agents in the system. It includes:

- **Unified Configuration**: Centralized pytest configuration with consistent settings
- **Shared Testing Utilities**: Reusable fixtures, mocks, and assertions
- **Performance Testing**: Built-in performance measurement and validation
- **Multi-level Testing**: Unit, integration, e2e, and performance tests
- **CI/CD Integration**: Automated test execution and reporting

## Quick Start

### 1. Setup Test Environment

```bash
# Run the setup script to install dependencies and create test structure
./scripts/setup_test_env.sh

# Or setup manually
pip install pytest pytest-asyncio pytest-cov pytest-xdist pytest-mock psutil pyyaml
```

### 2. Run Tests

```bash
# Basic test execution
./scripts/run_tests.sh

# Advanced test execution with comprehensive features
python scripts/advanced_test_runner.py --suite all --verbose

# Run specific test types
./scripts/run_tests.sh -t unit
./scripts/run_tests.sh -t integration
./scripts/run_tests.sh -t e2e
./scripts/run_tests.sh -t performance

# Advanced test suites
python scripts/advanced_test_runner.py --suite performance
python scripts/advanced_test_runner.py --suite unit --output results.json

# Run with coverage
./scripts/run_tests.sh -c html

# Run with verbose output and parallel execution
./scripts/run_tests.sh -v -p
```

### 3. Write Tests

```python
from backend.agents.shared.testing import (
    BaseAgentTest, StateFactory, AgentAssertions,
    LoadTester, ChaosEngineer, WorkflowValidator
)

class TestMyAgent(BaseAgentTest):
    @pytest.mark.unit
    async def test_my_agent_functionality(self):
        state = StateFactory.create_empty_state()
        # Your test code here
        AgentAssertions.assert_state_valid(state)

    @pytest.mark.performance
    async def test_agent_performance(self, load_tester):
        async def agent_operation():
            # Your agent operation
            return {"status": "completed"}

        results = await load_tester.run_load_test(agent_operation)
        assert results["success_rate"] >= 0.95
```

## Directory Structure

```
backend/agents/
├── conftest.py                    # Central pytest configuration
├── pytest.ini                    # Pytest settings
├── scripts/
│   ├── run_tests.sh              # Test execution script
│   └── setup_test_env.sh         # Environment setup script
├── shared/
│   └── testing/
│       ├── __init__.py
│       ├── base_test.py          # Base test class
│       ├── fixtures.py           # Shared fixtures
│       ├── mocks.py              # Mock utilities
│       ├── assertions.py         # Custom assertions
│       ├── factories.py          # Test data factories
│       ├── performance.py        # Performance testing
│       └── test_data.py          # Test data management
└── tests/
    ├── unit/                     # Unit tests
    ├── integration/              # Integration tests
    ├── e2e/                      # End-to-end tests
    └── performance/              # Performance tests
```

## Testing Components

### Base Test Class

The `BaseAgentTest` class provides common testing utilities:

```python
class TestMyAgent(BaseAgentTest):
    def setup_method(self):
        # Automatic setup for each test
        pass

    async def test_with_performance_measurement(self):
        result, duration = await self.measure_performance(my_async_operation())
        self.assert_performance_within_threshold(duration)
```

### Advanced Testing Utilities

#### Load Testing

Test system performance under various load conditions:

```python
from backend.agents.shared.testing import LoadTester, LoadTestConfig

# Configure load test
config = LoadTestConfig(
    concurrent_users=20,
    requests_per_user=10,
    ramp_up_time=2.0
)
load_tester = LoadTester(config)

# Run load test
async def my_operation():
    # Your operation to test
    return {"status": "completed"}

results = await load_tester.run_load_test(my_operation)
assert results["success_rate"] >= 0.95
assert results["avg_response_time"] < 2.0
```

#### Chaos Engineering

Test system resilience with controlled failure injection:

```python
from backend.agents.shared.testing import ChaosEngineer, ChaosConfig

# Configure chaos engineering
config = ChaosConfig(
    failure_rate=0.2,
    latency_injection=True,
    latency_min=0.1,
    latency_max=1.0
)
chaos_engineer = ChaosEngineer(config)

# Test with chaos injection
async with chaos_engineer.inject_chaos():
    # Your test code here - some operations may fail or be delayed
    result = await my_resilient_operation()
    # Verify system handles failures gracefully
```

#### Workflow Validation

Validate complex multi-step workflows:

```python
from backend.agents.shared.testing import WorkflowValidator

validator = WorkflowValidator()

# Add validation rules
def validate_step_order(trace):
    steps = [step["step"] for step in trace]
    expected = ["initialize", "execute", "cleanup"]
    return all(step in steps for step in expected)

validator.add_validation_rule("step_order", validate_step_order)

# Trace workflow execution
validator.trace_execution("initialize", {"agent": "TestAgent"})
validator.trace_execution("execute", {"result": "success"})
validator.trace_execution("cleanup", {"status": "complete"})

# Validate workflow
results = validator.validate_workflow()
assert results["passed"]
```

### State Factory

Create test states with various configurations:

```python
# Empty state
state = StateFactory.create_empty_state()

# State with messages
messages = [{"role": "user", "content": "Hello"}]
state = StateFactory.create_state_with_messages(messages)

# Conversation state
state = StateFactory.create_conversation_state(conversation_length=3)

# Multi-tenant states
states = StateFactory.create_multi_tenant_states(tenant_count=3)
```

### Mock Manager

Centralized mock management:

```python
mock_manager = MockManager()

# Create mock agent
mock_agent = mock_manager.create_mock_agent("TestAgent")

# Create mock external services
mock_supabase = mock_manager.create_mock_supabase_client()
mock_pinecone = mock_manager.create_mock_pinecone_client()

# Create mock tool executor
mock_executor = mock_manager.create_mock_tool_executor(tool_responses)

# Cleanup all mocks
mock_manager.cleanup()
```

### Custom Assertions

Specialized assertions for agent testing:

```python
# State validation
AgentAssertions.assert_state_valid(state)
AgentAssertions.assert_tenant_isolation(state, "tenant-123")

# Message validation
AgentAssertions.assert_message_count(state, 2)
AgentAssertions.assert_message_added(state, "user", "Hello")
AgentAssertions.assert_message_contains(state, "specific content")

# Agent execution validation
AgentAssertions.assert_agent_lifecycle_completed(mock_agent)
AgentAssertions.assert_tool_executed(mock_executor, "tool_name", 1)

# Performance validation
AgentAssertions.assert_execution_time(duration, max_duration)
```

### Performance Testing

Built-in performance measurement and validation:

```python
performance_tester = PerformanceTester()

# Measure single operation
result, metrics = await performance_tester.measure_async_operation(operation)

# Measure batch operations
results, metrics = await performance_tester.measure_batch_operations(
    operation, batch_size=10
)

# Measure concurrent operations
results, metrics = await performance_tester.measure_concurrent_operations(
    operation, concurrency_level=5, operation_count=20
)

# Validate performance
performance_tester.assert_performance_acceptable(metrics)
```

## Test Types and Markers

### Unit Tests (`@pytest.mark.unit`)

Test individual components in isolation:

```python
@pytest.mark.unit
async def test_agent_initialization(self):
    agent = create_test_agent()
    state = StateFactory.create_empty_state()
    
    result = await agent.initialize(state)
    
    AgentAssertions.assert_state_valid(result)
```

### Integration Tests (`@pytest.mark.integration`)

Test component interactions:

```python
@pytest.mark.integration
async def test_agent_tool_integration(self):
    agent = create_test_agent()
    mock_executor = create_mock_tool_executor()
    
    # Test agent using tools
    result = await execute_agent_with_tools(agent, mock_executor)
    
    AgentAssertions.assert_tool_executed(mock_executor, "expected_tool")
```

### End-to-End Tests (`@pytest.mark.e2e`)

Test complete workflows:

```python
@pytest.mark.e2e
async def test_complete_research_workflow(self):
    # Setup complete environment
    agents = setup_research_agents()
    external_services = setup_mock_services()
    
    # Execute complete workflow
    result = await execute_research_workflow(user_query)
    
    # Verify end-to-end functionality
    assert_workflow_completed_successfully(result)
```

### Performance Tests (`@pytest.mark.performance`)

Test performance characteristics:

```python
@pytest.mark.performance
async def test_agent_execution_performance(self, performance_tester):
    agent = create_test_agent()
    
    result, metrics = await performance_tester.measure_async_operation(
        execute_agent, agent
    )
    
    performance_tester.assert_performance_acceptable(metrics)
```

## Configuration

### Pytest Configuration (`pytest.ini`)

Key configuration options:

- **Test Discovery**: Automatic discovery of test files and functions
- **Async Support**: Built-in asyncio support for async tests
- **Coverage**: Comprehensive coverage reporting with configurable thresholds
- **Markers**: Custom markers for different test types
- **Logging**: Detailed logging configuration for debugging

### Environment Variables

Test environment variables (set automatically):

```bash
TESTING=true
LOG_LEVEL=DEBUG
TEST_DATABASE_URL=sqlite:///:memory:
# Mock API keys for external services
```

## Best Practices

### 1. Test Organization

- **One test class per component**: Keep tests focused and organized
- **Descriptive test names**: Use clear, descriptive test method names
- **Proper markers**: Use appropriate pytest markers for test categorization

### 2. State Management

- **Use factories**: Always use StateFactory for creating test states
- **Verify isolation**: Ensure tenant isolation in multi-tenant tests
- **Clean state**: Start each test with a clean state

### 3. Mocking

- **Mock external dependencies**: Always mock external APIs and services
- **Use MockManager**: Centralize mock creation and cleanup
- **Realistic mocks**: Create mocks that behave like real services

### 4. Assertions

- **Use custom assertions**: Leverage AgentAssertions for agent-specific validations
- **Comprehensive validation**: Validate both success and error scenarios
- **Performance validation**: Include performance assertions where appropriate

### 5. Performance Testing

- **Set realistic thresholds**: Configure performance thresholds based on requirements
- **Test different loads**: Test performance under various load conditions
- **Monitor resource usage**: Track memory and CPU usage during tests

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Agent Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          ./backend/agents/scripts/setup_test_env.sh --skip-data
      - name: Run tests
        run: ./backend/agents/scripts/run_tests.sh -c xml
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure PYTHONPATH includes project directories
2. **Async Test Failures**: Verify pytest-asyncio is installed and configured
3. **Mock Issues**: Check that mocks are properly reset between tests
4. **Performance Test Failures**: Adjust thresholds based on test environment

### Debug Mode

Run tests with debug logging:

```bash
./scripts/run_tests.sh -v --log-cli-level=DEBUG
```

### Test Data Issues

Reset test data:

```bash
rm -rf test_data/
./scripts/setup_test_env.sh --skip-deps
```

## Contributing

When adding new tests:

1. Follow the established patterns and conventions
2. Use appropriate test markers
3. Include both positive and negative test cases
4. Add performance tests for critical paths
5. Update documentation as needed

## Support

For questions or issues with the testing infrastructure:

1. Check this documentation
2. Review existing test examples
3. Check the troubleshooting section
4. Consult the team for complex scenarios
