name: Agent System Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/agents/**'
      - 'shared/core/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/agents/**'
      - 'shared/core/**'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
        test-type: [unit, integration]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        cd backend/agents
        ./scripts/setup_test_env.sh --skip-data

    - name: Run ${{ matrix.test-type }} tests
      run: |
        cd backend/agents
        ./scripts/run_tests.sh -t ${{ matrix.test-type }} -c xml

    - name: Upload coverage to Codecov
      if: matrix.test-type == 'unit' && matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/agents/coverage.xml
        flags: agents
        name: agent-coverage
        fail_ci_if_error: true

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}-${{ matrix.test-type }}
        path: |
          backend/agents/coverage.xml
          backend/agents/test_reports/

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        cd backend/agents
        ./scripts/setup_test_env.sh --skip-data

    - name: Run performance tests
      run: |
        cd backend/agents
        ./scripts/run_tests.sh -t performance -v

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: backend/agents/test_reports/

  e2e-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        cd backend/agents
        ./scripts/setup_test_env.sh

    - name: Run end-to-end tests
      run: |
        cd backend/agents
        ./scripts/run_tests.sh -t e2e -v

    - name: Upload e2e results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-results
        path: backend/agents/test_reports/

  security-scan:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run security scan
      uses: pypa/gh-action-pip-audit@v1.0.8
      with:
        inputs: requirements.txt

    - name: Run Bandit security linter
      run: |
        pip install bandit
        bandit -r backend/agents/ -f json -o bandit-report.json || true

    - name: Upload security results
      uses: actions/upload-artifact@v3
      with:
        name: security-results
        path: bandit-report.json

  quality-check:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install quality tools
      run: |
        pip install black isort flake8 mypy

    - name: Check code formatting
      run: |
        black --check backend/agents/
        isort --check-only backend/agents/

    - name: Run linting
      run: |
        flake8 backend/agents/ --max-line-length=88

    - name: Run type checking
      run: |
        mypy backend/agents/ --ignore-missing-imports || true

  notify:
    runs-on: ubuntu-latest
    needs: [test, performance-test, e2e-test, security-scan, quality-check]
    if: always()

    steps:
    - name: Notify on success
      if: needs.test.result == 'success'
      run: echo "✅ All agent tests passed successfully!"

    - name: Notify on failure
      if: needs.test.result == 'failure'
      run: |
        echo "❌ Agent tests failed!"
        exit 1
