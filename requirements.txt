# Core dependencies
aiohttp>=3.11.18,<4
annotated-types==0.7.0
anthropic==0.26.0          # LLM provider SDK
anyio==4.9.0
async-timeout==4.0.3
backoff>=2.2.1
bcrypt==4.3.0
celery>=5.3.6              # For async job processing
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
copilotkit==0.1.46
cryptography==44.0.3
distro==1.9.0
ecdsa==0.19.1
email-validator>=2.0.0
exceptiongroup==1.3.0
fastapi==0.115.12
google-cloud-storage==2.16.0      # pulls google-auth
google-generativeai==0.5.4 # Gemini client used in tests
grpcio>=1.62.0,<2.0.0      # Needed for gRPC-based clients
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1              # Single, unified pin
hypothesis>=6.92.1         # Found in test imports
idna==3.10
jinja2>=3.1.0              # Template engine for document generation
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
# Langchain packages with compatible versions
langchain>=0.3.25
langchain-community>=0.3.0     # For community models
langchain-core>=0.3.58        # Compatible with langchain 0.3.25
langchain-openai>=0.3.17
langchain-text-splitters>=0.3.8
langsmith>=0.1.126            # Compatible version
langgraph==0.2.50
langgraph-checkpoint==2.0.25
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.69
# langsmith entry moved above to fix dependency conflicts
numpy>=2.0.0,<3.0.0
openai>=1.3.7
orjson==3.10.18
ormsgpack==1.9.1
packaging==24.2
pandas>=2.0.0
partialjson==0.0.8
passlib==1.7.4
pinecone>=5.1.0
psycopg2-binary>=2.9.9     # For PostgreSQL database access
pyasn1==0.4.8
pycparser==2.22
pydantic==2.11.4
pydantic_core==2.33.2
PyJWT>=2.9.0
python-dateutil==2.8.2
python-dotenv==1.1.0
python-jose==3.4.0
python-multipart>=0.0.6
PyYAML==6.0.2
redis>=5.0.0               # For Redis client and Celery broker
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rsa==4.9.1
scipy>=1.10.0
sentence-transformers>=2.2.2
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
starlette==0.46.2
supabase>=2.15,<3
tenacity==9.1.2
tiktoken==0.9.0
toml==0.10.2
tqdm>=4.66.2
typing-inspection==0.4.0
typing_extensions==4.13.2
urllib3==2.4.0
uvicorn==0.34.2
voyageai>=0.1.6
xxhash==3.5.0
zstandard==0.23.0

# provider SDKs & misc libs
groq==0.10.0
psutil==5.9.8

# Test dependencies
pytest==8.3.5              # Using latest version found
pytest-asyncio==0.26.0     # Using latest version found
pytest-cov==4.1.0          # For coverage reports
pytest-mock==3.14.0        # Using latest version found
pytest-timeout==2.3.1      # For test timeouts


# Missing dependencies from CI errors
dateparser==1.2.0          # For date parsing
asyncpg==0.29.0            # For async PostgreSQL
pdf2image==1.17.0          # For PDF processing

# Performance testing dependencies
locust==2.24.0             # For load testing
ruff==0.3.4                # For linting

# Metrics and monitoring
prometheus-client==0.20.0  # For Prometheus metrics

