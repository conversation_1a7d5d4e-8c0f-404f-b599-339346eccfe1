-- Migration: Security Events Table for Enhanced Middleware Logging
-- Description: Creates table for logging security events from enhanced middleware

-- Create security_events table in the security schema
CREATE TABLE IF NOT EXISTS security.security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Event identification
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Request information
    ip_address TEXT,
    user_agent TEXT,
    request_url TEXT NOT NULL,
    request_method TEXT NOT NULL,
    request_headers JSONB DEFAULT '{}',
    
    -- Event details
    event_details JSONB DEFAULT '{}',
    
    -- User context (if available)
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    tenant_id UUID,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexing for performance
    CONSTRAINT valid_event_type CHECK (event_type IN (
        'authentication_failure',
        'authorization_failure', 
        'suspicious_request',
        'rate_limit_exceeded',
        'invalid_session',
        'brute_force_attempt',
        'csrf_violation',
        'malicious_payload',
        'unauthorized_access',
        'session_hijack_attempt'
    ))
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security.security_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security.security_events(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security.security_events(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_security_events_tenant_id ON security.security_events(tenant_id) WHERE tenant_id IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_security_events_type_severity ON security.security_events(event_type, severity);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_time ON security.security_events(ip_address, created_at DESC);

-- Row Level Security (RLS) policies
ALTER TABLE security.security_events ENABLE ROW LEVEL SECURITY;

-- Policy: Super admins can view all security events
CREATE POLICY "Super admins can view all security events" ON security.security_events
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.email = ANY(string_to_array(current_setting('app.super_admin_emails', true), ','))
                OR auth.users.user_metadata->>'is_super_admin' = 'true'
                OR auth.users.app_metadata->>'is_super_admin' = 'true'
            )
        )
    );

-- Policy: System can insert security events (for API logging)
CREATE POLICY "System can insert security events" ON security.security_events
    FOR INSERT
    WITH CHECK (true); -- Allow all inserts for system logging

-- Policy: Users can view their own security events
CREATE POLICY "Users can view their own security events" ON security.security_events
    FOR SELECT
    USING (user_id = auth.uid());

-- Function to clean up old security events (data retention)
CREATE OR REPLACE FUNCTION security.cleanup_old_security_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    retention_days INTEGER := 90; -- Default 90 days retention
BEGIN
    -- Get retention period from configuration (if available)
    BEGIN
        retention_days := current_setting('app.security_events_retention_days')::INTEGER;
    EXCEPTION WHEN OTHERS THEN
        retention_days := 90; -- Fallback to 90 days
    END;
    
    -- Delete old security events
    DELETE FROM security.security_events
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup activity
    INSERT INTO security.security_events (
        event_type,
        severity,
        request_url,
        request_method,
        event_details
    ) VALUES (
        'system_maintenance',
        'low',
        '/system/cleanup',
        'SYSTEM',
        jsonb_build_object(
            'action', 'cleanup_old_security_events',
            'deleted_count', deleted_count,
            'retention_days', retention_days
        )
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get security event statistics
CREATE OR REPLACE FUNCTION security.get_security_event_stats(
    start_date TIMESTAMPTZ DEFAULT NOW() - INTERVAL '24 hours',
    end_date TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    event_type TEXT,
    severity TEXT,
    event_count BIGINT,
    unique_ips BIGINT,
    latest_event TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        se.event_type,
        se.severity,
        COUNT(*) as event_count,
        COUNT(DISTINCT se.ip_address) as unique_ips,
        MAX(se.created_at) as latest_event
    FROM security.security_events se
    WHERE se.created_at BETWEEN start_date AND end_date
    GROUP BY se.event_type, se.severity
    ORDER BY event_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to detect potential security threats
CREATE OR REPLACE FUNCTION security.detect_security_threats(
    lookback_hours INTEGER DEFAULT 1
)
RETURNS TABLE (
    threat_type TEXT,
    ip_address TEXT,
    event_count BIGINT,
    severity_level TEXT,
    first_seen TIMESTAMPTZ,
    last_seen TIMESTAMPTZ,
    threat_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH threat_analysis AS (
        SELECT 
            se.ip_address,
            se.event_type,
            se.severity,
            COUNT(*) as event_count,
            MIN(se.created_at) as first_seen,
            MAX(se.created_at) as last_seen,
            -- Calculate threat score based on event type and frequency
            CASE 
                WHEN se.event_type IN ('malicious_payload', 'session_hijack_attempt') THEN COUNT(*) * 10
                WHEN se.event_type IN ('brute_force_attempt', 'unauthorized_access') THEN COUNT(*) * 5
                WHEN se.event_type IN ('authorization_failure', 'csrf_violation') THEN COUNT(*) * 3
                ELSE COUNT(*)
            END as calculated_threat_score
        FROM security.security_events se
        WHERE se.created_at >= NOW() - INTERVAL '1 hour' * lookback_hours
        AND se.ip_address IS NOT NULL
        GROUP BY se.ip_address, se.event_type, se.severity
    )
    SELECT 
        ta.event_type as threat_type,
        ta.ip_address,
        ta.event_count,
        ta.severity,
        ta.first_seen,
        ta.last_seen,
        ta.calculated_threat_score as threat_score
    FROM threat_analysis ta
    WHERE ta.calculated_threat_score >= 10 -- Threshold for threat detection
    ORDER BY ta.calculated_threat_score DESC, ta.last_seen DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT ON security.security_events TO authenticated;
GRANT EXECUTE ON FUNCTION security.cleanup_old_security_events() TO authenticated;
GRANT EXECUTE ON FUNCTION security.get_security_event_stats(TIMESTAMPTZ, TIMESTAMPTZ) TO authenticated;
GRANT EXECUTE ON FUNCTION security.detect_security_threats(INTEGER) TO authenticated;

-- Create a scheduled job to clean up old security events (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-security-events', '0 2 * * *', 'SELECT security.cleanup_old_security_events();');

-- Add comment for documentation
COMMENT ON TABLE security.security_events IS 'Stores security events from enhanced middleware and security monitoring';
COMMENT ON FUNCTION security.cleanup_old_security_events() IS 'Cleans up old security events based on retention policy';
COMMENT ON FUNCTION security.get_security_event_stats(TIMESTAMPTZ, TIMESTAMPTZ) IS 'Returns security event statistics for a given time period';
COMMENT ON FUNCTION security.detect_security_threats(INTEGER) IS 'Detects potential security threats based on event patterns';
