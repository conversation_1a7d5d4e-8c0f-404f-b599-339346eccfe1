'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useIntakeAgent } from '@/hooks/use-intake-agent'

export function IntakeTest() {
  const [testDescription, setTestDescription] = useState('')
  const [classificationResult, setClassificationResult] = useState<any>(null)

  const { classifyMatter, isLoading } = useIntakeAgent({
    mode: 'staff',
    onComplete: (state) => console.log('Intake completed:', state),
    onError: (error) => console.error('Intake error:', error)
  })

  const handleClassify = async () => {
    if (!testDescription.trim()) return

    try {
      const result = await classifyMatter(testDescription)
      setClassificationResult(result)
    } catch (error) {
      console.error('Classification error:', error)
    }
  }

  const testCases = [
    // Simple cases (keyword-based)
    "I was in a car accident last week and injured my back",
    "I need help with my divorce and child custody",
    "I was arrested for DUI last night",

    // Complex cases requiring LLM reasoning
    "My ex-husband hasn't paid child support in 6 months and now he's threatening to take the kids away from me. He also has a history of domestic violence and I'm scared for my safety and my children's wellbeing.",

    "I was working on a construction site when the scaffolding collapsed. Three other workers were injured too. The company knew the equipment was faulty but didn't fix it. Now they're trying to blame us for not following safety protocols.",

    "I got pulled over for speeding but the officer said I was acting suspicious and searched my car without permission. They found some pills that belong to my grandmother - I was taking them to her nursing home. Now I'm charged with drug possession and they want to take my license away.",

    "My business partner and I started a company together 5 years ago. Now he's trying to force me out and steal all our clients. He's also been embezzling money and I have proof. I need to protect my business and get him prosecuted.",

    "I have court tomorrow morning for my DUI case and my lawyer just dropped me. The prosecutor is offering a plea deal but I don't understand what it means. I could lose my job if I get convicted.",

    "My teenage daughter was sexually assaulted at school and the administration is covering it up. They're saying it was consensual but she's only 15. The boy's father is on the school board and they're protecting him."
  ]

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Intake Agent Classification Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={testDescription}
              onChange={(e) => setTestDescription(e.target.value)}
              placeholder="Describe your legal matter..."
              className="flex-1"
            />
            <Button 
              onClick={handleClassify}
              disabled={isLoading || !testDescription.trim()}
            >
              {isLoading ? 'Classifying...' : 'Classify'}
            </Button>
          </div>

          {classificationResult && (
            <div className="p-4 border rounded-lg space-y-4">
              <div className="flex items-center gap-2 flex-wrap">
                <Badge variant="outline">
                  {classificationResult.practice_area.replace('_', ' ').toUpperCase()}
                </Badge>
                <Badge variant="secondary">
                  {classificationResult.display_label}
                </Badge>
                <Badge variant={
                  classificationResult.urgency === 'critical' ? 'destructive' :
                  classificationResult.urgency === 'high' ? 'destructive' :
                  classificationResult.urgency === 'medium' ? 'default' : 'secondary'
                }>
                  {classificationResult.urgency.toUpperCase()}
                </Badge>
                {classificationResult.fallback_used && (
                  <Badge variant="destructive">FALLBACK USED</Badge>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <h4 className="font-semibold">Basic Classification</h4>
                  <p><strong>Case Type:</strong> {classificationResult.case_type.replace('_', ' ')}</p>
                  <p><strong>Work Type:</strong> {classificationResult.work_type}</p>
                  <p><strong>Confidence:</strong> {Math.round(classificationResult.confidence * 100)}%</p>
                  {classificationResult.keywords_matched.length > 0 && (
                    <p><strong>Keywords:</strong> {classificationResult.keywords_matched.join(', ')}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h4 className="font-semibold">LLM Insights</h4>
                  {classificationResult.key_factors.length > 0 && (
                    <div>
                      <strong>Key Factors:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {classificationResult.key_factors.map((factor, i) => (
                          <li key={i} className="text-xs">{factor}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {classificationResult.timeline_factors.length > 0 && (
                    <div>
                      <strong>Timeline Factors:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {classificationResult.timeline_factors.map((factor, i) => (
                          <li key={i} className="text-xs">{factor}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {classificationResult.parties_involved.length > 0 && (
                    <div>
                      <strong>Parties:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {classificationResult.parties_involved.map((party, i) => (
                          <li key={i} className="text-xs">{party}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              {classificationResult.red_flags.length > 0 && (
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded">
                  <h4 className="font-semibold text-destructive mb-2">⚠️ Red Flags</h4>
                  <ul className="list-disc list-inside text-sm">
                    {classificationResult.red_flags.map((flag, i) => (
                      <li key={i} className="text-destructive">{flag}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="space-y-2">
                <h4 className="font-semibold">Reasoning</h4>
                <p className="text-sm bg-muted p-2 rounded">{classificationResult.llm_reasoning || classificationResult.reasoning}</p>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-semibold">Test Cases:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {testCases.map((testCase, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setTestDescription(testCase)}
                  className="text-left justify-start h-auto p-2"
                >
                  <span className="text-xs">{testCase}</span>
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
