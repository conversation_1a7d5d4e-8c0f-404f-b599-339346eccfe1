'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { ErrorBoundary } from 'react-error-boundary'
import { toast } from 'sonner'
import { useIntakeAgent } from '@/hooks/use-intake-agent'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  getMatterDisplayLabel,
  PracticeArea,
  WorkType,
  getPracticeAreaDisplayName
} from '@/types/domain/tenants/Matter'
import { SendIcon, UserIcon, BotIcon } from 'lucide-react'

interface IntakeProgress {
  currentStep: string
  completedSteps: string[]
  totalSteps: number
  practiceArea?: string
  workType?: string
  displayLabel?: string
  urgency?: string
}

interface MultiPracticeIntakeProps {
  mode: 'client' | 'staff'
  onComplete?: (result: any) => void
}

export function MultiPracticeIntake({ mode, onComplete }: MultiPracticeIntakeProps) {
  const router = useRouter()
  const [currentMessage, setCurrentMessage] = useState('')
  const [isStarted, setIsStarted] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const {
    state,
    isLoading,
    error,
    startIntake,
    continueIntake,
    getProgress,
    getDisplayLabel,
    getUrgencyLevel,
    getPracticeArea
  } = useIntakeAgent({
    mode,
    onComplete: (completedState) => {
      toast.success('Intake completed successfully!')
      onComplete?.(completedState)
    },
    onError: (error) => {
      console.error('Intake error:', error)
      toast.error('An error occurred during intake')
    }
  })

  const progress = getProgress()

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [state?.messages])

  // Start intake on mount
  useEffect(() => {
    if (!isStarted) {
      setIsStarted(true)
      startIntake()
    }
  }, [isStarted, startIntake])

  const handleSendMessage = async () => {
    if (!currentMessage.trim() || isLoading) return

    try {
      await continueIntake(currentMessage)
      setCurrentMessage('')
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getStepDisplayName = (step: string): string => {
    const stepNames: Record<string, string> = {
      'initial_contact': 'Initial Contact',
      'collect_personal_info': 'Personal Information',
      'collect_case_details': 'Matter Details',
      'practice_area_details': 'Practice Area Details',
      'check_conflicts': 'Conflict Check',
      'summarize_and_confirm': 'Review & Confirm',
      'save_client_info': 'Save Information',
      'completed': 'Completed'
    }
    return stepNames[step] || step.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getProgressPercentage = (): number => {
    return progress.percentage
  }

  const getUrgencyColor = (urgency?: string): string => {
    switch (urgency) {
      case 'critical': return 'destructive'
      case 'high': return 'destructive'
      case 'medium': return 'default'
      case 'low': return 'secondary'
      default: return 'secondary'
    }
  }

  const renderMessage = (message: any, index: number) => {
    const isAI = message.type === 'ai' || message.type === 'system'

    return (
      <div key={index} className={`flex gap-3 ${isAI ? '' : 'flex-row-reverse'}`}>
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isAI ? 'bg-primary text-primary-foreground' : 'bg-muted'
        }`}>
          {isAI ? <BotIcon className="w-4 h-4" /> : <UserIcon className="w-4 h-4" />}
        </div>
        <div className={`flex-1 max-w-[80%] ${isAI ? '' : 'text-right'}`}>
          <div className={`inline-block p-3 rounded-lg ${
            isAI
              ? 'bg-muted text-muted-foreground'
              : 'bg-primary text-primary-foreground'
          }`}>
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">
                {mode === 'staff' ? 'Staff Intake' : 'Client Intake'}
                {getDisplayLabel() && (
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    ({getDisplayLabel()})
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                {getStepDisplayName(progress.currentStep || 'initial_contact')}
                {getPracticeArea() && (
                  <span className="ml-2">
                    • {getPracticeAreaDisplayName(getPracticeArea() as PracticeArea)}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {getUrgencyLevel() && getUrgencyLevel() !== 'low' && (
                <Badge variant={getUrgencyColor(getUrgencyLevel()) as any}>
                  {getUrgencyLevel().toUpperCase()}
                </Badge>
              )}
              <Badge variant="outline">
                {progress.current}/{progress.total}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress</span>
              <span>{getProgressPercentage()}%</span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <Card>
        <CardContent className="p-0">
          <div className="flex flex-col h-[600px]">
            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {state?.messages?.map((message, index) => renderMessage(message, index))}
                {isLoading && (
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center">
                      <BotIcon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="inline-block p-3 rounded-lg bg-muted text-muted-foreground">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Input */}
            <div className="border-t p-4">
              <div className="flex gap-2">
                <Input
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={isLoading || !currentMessage.trim()}
                  size="icon"
                >
                  <SendIcon className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step Indicator */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Intake Steps</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
            {[
              'initial_contact',
              'collect_personal_info', 
              'collect_case_details',
              'practice_area_details',
              'check_conflicts',
              'summarize_and_confirm',
              'save_client_info'
            ].map((step, index) => {
              const isCompleted = progress.completedSteps.includes(step)
              const isCurrent = progress.currentStep === step
              
              return (
                <div
                  key={step}
                  className={`p-2 rounded text-xs text-center transition-colors ${
                    isCompleted 
                      ? 'bg-primary text-primary-foreground' 
                      : isCurrent 
                        ? 'bg-primary/20 text-primary border border-primary' 
                        : 'bg-muted text-muted-foreground'
                  }`}
                >
                  <div className="font-medium">{index + 1}</div>
                  <div className="mt-1 leading-tight">
                    {getStepDisplayName(step)}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
