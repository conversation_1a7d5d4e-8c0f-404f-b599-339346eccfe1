/**
 * Enhanced Security Utilities for Middleware
 * 
 * This module provides comprehensive security utilities for the Next.js middleware
 * including security headers, request validation, suspicious activity detection,
 * and security event logging.
 */

import { NextRequest, NextResponse } from 'next/server';

// Security event types for logging and monitoring
export enum SecurityEventType {
  AUTHENTICATION_FAILURE = 'authentication_failure',
  AUTHORIZATION_FAILURE = 'authorization_failure',
  SUSPICIOUS_REQUEST = 'suspicious_request',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  INVALID_SESSION = 'invalid_session',
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',
  CSRF_VIOLATION = 'csrf_violation',
  MALICIOUS_PAYLOAD = 'malicious_payload',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SESSION_HIJACK_ATTEMPT = 'session_hijack_attempt'
}

// Security configuration
const SECURITY_CONFIG = {
  MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_HEADER_SIZE: 8192, // 8KB
  MAX_URL_LENGTH: 2048,
  SUSPICIOUS_USER_AGENTS: [
    'sqlmap',
    'nikto',
    'nmap',
    'masscan',
    'burp',
    'owasp',
    'dirbuster'
  ],
  BLOCKED_EXTENSIONS: [
    '.php',
    '.asp',
    '.aspx',
    '.jsp',
    '.cgi',
    '.pl'
  ],
  RATE_LIMIT_WINDOW: 60 * 1000, // 1 minute
  RATE_LIMIT_MAX_REQUESTS: 100
};

// In-memory store for rate limiting and suspicious activity tracking
const securityStore = new Map<string, {
  requests: number[];
  suspiciousActivity: number;
  lastActivity: number;
  blockedUntil?: number;
}>();

/**
 * Add comprehensive security headers to the response
 */
export function addSecurityHeaders(response: NextResponse, request: NextRequest): NextResponse {
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://challenges.cloudflare.com *.fpjs.io *.fpcdn.io fpnpmcdn.net https://vercel.live",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.cloud.copilotkit.ai *.fpjs.io *.fpcdn.io https://vercel.live",
    "frame-ancestors 'none'",
    "form-action 'self'",
    "base-uri 'self'",
    "object-src 'none'",
    "upgrade-insecure-requests"
  ].join('; ');

  // Security headers
  const securityHeaders = {
    // Content Security Policy
    'Content-Security-Policy': csp,
    
    // XSS Protection
    'X-XSS-Protection': '1; mode=block',
    
    // Content Type Options
    'X-Content-Type-Options': 'nosniff',
    
    // Frame Options
    'X-Frame-Options': 'DENY',
    
    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions Policy
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()',
    
    // HSTS (HTTP Strict Transport Security)
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    
    // Cache Control for sensitive pages
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    
    // Additional security headers
    'X-Permitted-Cross-Domain-Policies': 'none',
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'same-origin'
  };

  // Apply headers to response
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Add security identifier header for monitoring
  response.headers.set('X-Security-Version', '1.3.1');
  response.headers.set('X-Request-ID', crypto.randomUUID());

  return response;
}

/**
 * Validate incoming request for security threats
 */
export function validateRequest(request: NextRequest): {
  isValid: boolean;
  violations: string[];
  riskLevel: 'low' | 'medium' | 'high';
} {
  const violations: string[] = [];
  let riskLevel: 'low' | 'medium' | 'high' = 'low';

  // Check request size
  const contentLength = request.headers.get('content-length');
  if (contentLength && parseInt(contentLength) > SECURITY_CONFIG.MAX_REQUEST_SIZE) {
    violations.push('Request size exceeds maximum allowed');
    riskLevel = 'high';
  }

  // Check URL length
  if (request.url.length > SECURITY_CONFIG.MAX_URL_LENGTH) {
    violations.push('URL length exceeds maximum allowed');
    riskLevel = 'medium';
  }

  // Check for suspicious file extensions
  const pathname = request.nextUrl.pathname.toLowerCase();
  const hasSuspiciousExtension = SECURITY_CONFIG.BLOCKED_EXTENSIONS.some(ext => 
    pathname.endsWith(ext)
  );
  if (hasSuspiciousExtension) {
    violations.push('Request contains blocked file extension');
    riskLevel = 'high';
  }

  // Check User-Agent
  const userAgent = request.headers.get('user-agent')?.toLowerCase() || '';
  const hasSuspiciousUserAgent = SECURITY_CONFIG.SUSPICIOUS_USER_AGENTS.some(agent => 
    userAgent.includes(agent)
  );
  if (hasSuspiciousUserAgent) {
    violations.push('Suspicious user agent detected');
    riskLevel = 'high';
  }

  // Check for common attack patterns in URL
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /vbscript:/i,  // VBScript injection
    /onload=/i,  // Event handler injection
    /onerror=/i,  // Event handler injection
  ];

  const hasSuspiciousPattern = suspiciousPatterns.some(pattern => 
    pattern.test(request.url)
  );
  if (hasSuspiciousPattern) {
    violations.push('Malicious payload pattern detected');
    riskLevel = 'high';
  }

  // Check headers for suspicious content
  const headers = request.headers;
  headers.forEach((value, key) => {
    if (value.length > SECURITY_CONFIG.MAX_HEADER_SIZE) {
      violations.push(`Header ${key} exceeds maximum size`);
      riskLevel = 'medium';
    }
  });

  return {
    isValid: violations.length === 0,
    violations,
    riskLevel
  };
}

/**
 * Detect and track suspicious activity
 */
export function detectSuspiciousActivity(
  request: NextRequest,
  clientId: string
): {
  isSuspicious: boolean;
  shouldBlock: boolean;
  reason?: string;
} {
  const now = Date.now();
  const clientData = securityStore.get(clientId) || {
    requests: [],
    suspiciousActivity: 0,
    lastActivity: now
  };

  // Check if client is currently blocked
  if (clientData.blockedUntil && now < clientData.blockedUntil) {
    return {
      isSuspicious: true,
      shouldBlock: true,
      reason: 'Client temporarily blocked due to suspicious activity'
    };
  }

  // Clean old requests (outside rate limit window)
  const windowStart = now - SECURITY_CONFIG.RATE_LIMIT_WINDOW;
  clientData.requests = clientData.requests.filter(timestamp => timestamp > windowStart);

  // Add current request
  clientData.requests.push(now);
  clientData.lastActivity = now;

  // Check rate limiting
  if (clientData.requests.length > SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS) {
    clientData.suspiciousActivity++;
    
    // Block client if too many violations
    if (clientData.suspiciousActivity >= 3) {
      clientData.blockedUntil = now + (15 * 60 * 1000); // Block for 15 minutes
      securityStore.set(clientId, clientData);
      
      return {
        isSuspicious: true,
        shouldBlock: true,
        reason: 'Rate limit exceeded multiple times'
      };
    }

    securityStore.set(clientId, clientData);
    return {
      isSuspicious: true,
      shouldBlock: false,
      reason: 'Rate limit exceeded'
    };
  }

  // Update store
  securityStore.set(clientId, clientData);

  return {
    isSuspicious: false,
    shouldBlock: false
  };
}

/**
 * Log security events for monitoring and analysis
 */
export async function logSecurityEvent(
  eventType: SecurityEventType,
  request: NextRequest,
  details: Record<string, any> = {}
): Promise<void> {
  const securityEvent = {
    timestamp: new Date().toISOString(),
    eventType,
    ip: getClientIP(request),
    userAgent: request.headers.get('user-agent'),
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries()),
    details,
    severity: getSeverityLevel(eventType)
  };

  // Log to console for development
  if (process.env.NODE_ENV === 'development') {
    console.warn('🚨 SECURITY EVENT:', securityEvent);
  }

  // In production, this would send to a security monitoring service
  // For now, we'll use a simple API call to log the event
  try {
    await fetch('/api/security/log-event', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(securityEvent),
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwardedFor) return forwardedFor.split(',')[0].trim();
  
  return 'unknown';
}

/**
 * Get severity level for security event type
 */
function getSeverityLevel(eventType: SecurityEventType): 'low' | 'medium' | 'high' | 'critical' {
  const severityMap: Record<SecurityEventType, 'low' | 'medium' | 'high' | 'critical'> = {
    [SecurityEventType.AUTHENTICATION_FAILURE]: 'medium',
    [SecurityEventType.AUTHORIZATION_FAILURE]: 'high',
    [SecurityEventType.SUSPICIOUS_REQUEST]: 'medium',
    [SecurityEventType.RATE_LIMIT_EXCEEDED]: 'low',
    [SecurityEventType.INVALID_SESSION]: 'medium',
    [SecurityEventType.BRUTE_FORCE_ATTEMPT]: 'high',
    [SecurityEventType.CSRF_VIOLATION]: 'high',
    [SecurityEventType.MALICIOUS_PAYLOAD]: 'critical',
    [SecurityEventType.UNAUTHORIZED_ACCESS]: 'high',
    [SecurityEventType.SESSION_HIJACK_ATTEMPT]: 'critical'
  };

  return severityMap[eventType] || 'medium';
}
