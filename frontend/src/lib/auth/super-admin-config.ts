/**
 * Super Admin Configuration
 *
 * Secure configuration for super admin access that supports both environment-based
 * and database-driven super admin management. This replaces hardcoded email lists
 * with a more secure and flexible approach.
 */

import { getSafeEnvironmentConfig } from '@/config/env';
import { createClient } from '@/lib/supabase/server';
import { Database } from '@/lib/supabase/database.types';

/**
 * Database Super Admin Configuration Record
 */
export interface SuperAdminRecord {
  id: string;
  email: string;
  is_active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  notes: string | null;
}

/**
 * Super Admin Configuration Interface
 */
export interface SuperAdminConfig {
  emails: string[];
  isDatabaseDriven: boolean;
  isEnvironmentConfigured: boolean;
  databaseEmails: string[];
  environmentEmails: string[];
  source: 'environment' | 'database' | 'hybrid';
}

/**
 * Get super admin configuration from environment variables
 * This is the primary source for super admin emails in production
 */
function getEnvironmentSuperAdminEmails(): string[] {
  try {
    const config = getSafeEnvironmentConfig();
    return config?.superAdminEmails || [];
  } catch (error) {
    console.error('Failed to load super admin emails from environment:', error);
    return [];
  }
}

/**
 * Get super admin emails from database
 * This allows dynamic management of super admin users
 */
async function getDatabaseSuperAdminEmails(): Promise<string[]> {
  try {
    const supabase = createClient();

    // Query the security.super_admin_config table for active super admins
    const { data, error } = await supabase
      .from('super_admin_config')
      .select('email')
      .eq('is_active', true)
      .order('email');

    if (error) {
      console.error('Failed to fetch super admin emails from database:', error);
      return [];
    }

    return data?.map(record => record.email) || [];
  } catch (error) {
    console.error('Database super admin email fetch error:', error);
    return [];
  }
}

/**
 * Fallback super admin emails for development/testing
 * These should NOT be used in production
 */
const DEVELOPMENT_FALLBACK_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
] as const;

/**
 * Get the current super admin configuration (async version with database support)
 * Supports both environment variables and database-driven configuration
 */
export async function getSuperAdminConfigAsync(): Promise<SuperAdminConfig> {
  const environmentEmails = getEnvironmentSuperAdminEmails();
  const isEnvironmentConfigured = environmentEmails.length > 0;

  // Try to get database emails
  const databaseEmails = await getDatabaseSuperAdminEmails();
  const isDatabaseDriven = databaseEmails.length > 0;

  // Determine configuration source and combine emails
  let emails: string[];
  let source: 'environment' | 'database' | 'hybrid';

  if (isEnvironmentConfigured && isDatabaseDriven) {
    // Hybrid mode: combine both sources, prioritizing environment for consistency
    emails = [...new Set([...environmentEmails, ...databaseEmails])];
    source = 'hybrid';
  } else if (isDatabaseDriven) {
    // Database-driven mode
    emails = databaseEmails;
    source = 'database';
  } else if (isEnvironmentConfigured) {
    // Environment-driven mode
    emails = environmentEmails;
    source = 'environment';
  } else {
    // SECURITY: Strict production requirements - no fallbacks allowed
    if (process.env.NODE_ENV === 'production') {
      throw new Error(
        'CRITICAL SECURITY ERROR: No super admin configuration found in production. ' +
        'You must configure either:\n' +
        '1. SUPER_ADMIN_EMAILS environment variable (recommended), or\n' +
        '2. Add records to the super_admin_config database table.\n' +
        'This is required for security and cannot use fallback values in production.'
      );
    }

    // Development fallback only - never used in production
    console.warn(
      'DEVELOPMENT MODE: Using fallback super admin emails. ' +
      'Configure SUPER_ADMIN_EMAILS environment variable for production.'
    );
    emails = [...DEVELOPMENT_FALLBACK_EMAILS];
    source = 'environment';
  }

  return {
    emails,
    isDatabaseDriven,
    isEnvironmentConfigured,
    databaseEmails,
    environmentEmails,
    source
  };
}

/**
 * Get the current super admin configuration (synchronous version for backward compatibility)
 * This version only uses environment variables and fallbacks
 */
export function getSuperAdminConfig(): SuperAdminConfig {
  const environmentEmails = getEnvironmentSuperAdminEmails();
  const isEnvironmentConfigured = environmentEmails.length > 0;

  // SECURITY: Strict production requirements - no fallbacks allowed
  if (process.env.NODE_ENV === 'production' && !isEnvironmentConfigured) {
    throw new Error(
      'CRITICAL SECURITY ERROR: SUPER_ADMIN_EMAILS environment variable must be configured in production. ' +
      'Set SUPER_ADMIN_EMAILS="<EMAIL>,<EMAIL>" or use JSON format. ' +
      'Fallback values are not allowed in production for security reasons.'
    );
  }

  // Use environment emails if available, otherwise fallback for development only
  let emails: string[];
  if (isEnvironmentConfigured) {
    emails = environmentEmails;
  } else {
    // Development fallback only - never used in production
    console.warn(
      'DEVELOPMENT MODE: Using fallback super admin emails. ' +
      'Configure SUPER_ADMIN_EMAILS environment variable for production.'
    );
    emails = [...DEVELOPMENT_FALLBACK_EMAILS];
  }

  return {
    emails,
    isDatabaseDriven: false, // This sync version doesn't check database
    isEnvironmentConfigured,
    databaseEmails: [],
    environmentEmails,
    source: 'environment'
  };
}

/**
 * Check if an email is configured as a super admin email (async version with database support)
 * This version checks both environment and database sources
 */
export async function isSuperAdminEmailAsync(email: string | null | undefined): Promise<boolean> {
  if (!email) return false;

  try {
    const config = await getSuperAdminConfigAsync();
    return config.emails.includes(email);
  } catch (error) {
    console.error('Failed to check super admin email (async):', error);
    return false;
  }
}

/**
 * Check if an email is configured as a super admin email (synchronous version)
 * This version only checks environment variables for backward compatibility
 */
export function isSuperAdminEmail(email: string | null | undefined): boolean {
  if (!email) return false;

  try {
    const config = getSuperAdminConfig();
    return config.emails.includes(email);
  } catch (error) {
    console.error('Failed to check super admin email:', error);
    return false;
  }
}

/**
 * Get all configured super admin emails (async version with database support)
 */
export async function getSuperAdminEmailsAsync(): Promise<string[]> {
  try {
    const config = await getSuperAdminConfigAsync();
    return [...config.emails]; // Return a copy to prevent mutation
  } catch (error) {
    console.error('Failed to get super admin emails (async):', error);
    return [];
  }
}

/**
 * Get all configured super admin emails (synchronous version)
 * Use this instead of importing SUPER_ADMIN_EMAILS directly
 */
export function getSuperAdminEmails(): string[] {
  try {
    const config = getSuperAdminConfig();
    return [...config.emails]; // Return a copy to prevent mutation
  } catch (error) {
    console.error('Failed to get super admin emails:', error);
    return [];
  }
}

/**
 * Database Management Functions for Super Admin Configuration
 */

/**
 * Add a new super admin email to the database
 */
export async function addSuperAdminEmail(
  email: string,
  createdBy: string,
  notes?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { success: false, error: 'Invalid email format' };
    }

    const supabase = createClient();

    // Check if email already exists
    const { data: existing } = await supabase
      .from('super_admin_config')
      .select('id')
      .eq('email', email)
      .single();

    if (existing) {
      return { success: false, error: 'Email already exists in super admin configuration' };
    }

    // Insert new super admin record
    const { error } = await supabase
      .from('super_admin_config')
      .insert({
        email,
        is_active: true,
        created_by: createdBy,
        updated_by: createdBy,
        notes
      });

    if (error) {
      console.error('Failed to add super admin email:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error adding super admin email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Remove a super admin email from the database (deactivate)
 */
export async function removeSuperAdminEmail(
  email: string,
  updatedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient();

    // Deactivate the super admin record instead of deleting for audit trail
    const { error } = await supabase
      .from('super_admin_config')
      .update({
        is_active: false,
        updated_by: updatedBy,
        updated_at: new Date().toISOString()
      })
      .eq('email', email);

    if (error) {
      console.error('Failed to remove super admin email:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error removing super admin email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get all super admin records from database (for management interface)
 */
export async function getAllSuperAdminRecords(): Promise<SuperAdminRecord[]> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('super_admin_config')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch super admin records:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching super admin records:', error);
    return [];
  }
}

/**
 * Validate super admin configuration (async version with database support)
 */
export async function validateSuperAdminConfigAsync(): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const config = await getSuperAdminConfigAsync();

    // Check if any emails are configured
    if (config.emails.length === 0) {
      errors.push('No super admin emails configured');
    }

    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = config.emails.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      errors.push(`Invalid email format(s): ${invalidEmails.join(', ')}`);
    }

    // Check for development fallback usage in production
    if (process.env.NODE_ENV === 'production' && !config.isEnvironmentConfigured && !config.isDatabaseDriven) {
      errors.push('No super admin configuration found in production');
    }

    // Warn about configuration source
    if (config.source === 'hybrid') {
      warnings.push('Super admins configured in both environment and database. Environment emails take precedence.');
    } else if (!config.isEnvironmentConfigured && !config.isDatabaseDriven) {
      warnings.push('Using development fallback super admin emails. Configure SUPER_ADMIN_EMAILS environment variable or add database records for production.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings
    };
  }
}

/**
 * Validate super admin configuration (synchronous version for backward compatibility)
 * This should be called during application startup
 */
export function validateSuperAdminConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const config = getSuperAdminConfig();

    // Check if any emails are configured
    if (config.emails.length === 0) {
      errors.push('No super admin emails configured');
    }

    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = config.emails.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      errors.push(`Invalid email format(s): ${invalidEmails.join(', ')}`);
    }

    // Check for development fallback usage in production
    if (process.env.NODE_ENV === 'production' && !config.isEnvironmentConfigured) {
      errors.push('Using development fallback emails in production');
    }

    // Warn about development fallback usage
    if (!config.isEnvironmentConfigured) {
      warnings.push('Using development fallback super admin emails. Set SUPER_ADMIN_EMAILS environment variable for production.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings
    };
  }
}

/**
 * Log super admin configuration status (async version with database support)
 */
export async function logSuperAdminConfigStatusAsync(): Promise<void> {
  try {
    const validation = await validateSuperAdminConfigAsync();
    const config = await getSuperAdminConfigAsync();

    console.log('Super Admin Configuration Status (Database-Enhanced):');
    console.log(`  Environment Configured: ${config.isEnvironmentConfigured}`);
    console.log(`  Database Driven: ${config.isDatabaseDriven}`);
    console.log(`  Configuration Source: ${config.source}`);
    console.log(`  Total Emails: ${config.emails.length}`);
    console.log(`  Environment Emails: ${config.environmentEmails.length}`);
    console.log(`  Database Emails: ${config.databaseEmails.length}`);
    console.log(`  Valid: ${validation.isValid}`);

    if (validation.warnings.length > 0) {
      console.warn('Super Admin Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    if (validation.errors.length > 0) {
      console.error('Super Admin Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
    }

    // In development, show configured emails (masked)
    if (process.env.NODE_ENV === 'development') {
      console.log('Configured Super Admin Emails (masked):');
      config.emails.forEach(email => {
        const [local, domain] = email.split('@');
        const maskedLocal = local.length > 2 ? `${local[0]}***${local[local.length - 1]}` : '***';
        const source = config.environmentEmails.includes(email)
          ? (config.databaseEmails.includes(email) ? 'hybrid' : 'env')
          : 'db';
        console.log(`  - ${maskedLocal}@${domain} (${source})`);
      });
    }
  } catch (error) {
    console.error('Failed to log super admin configuration status (async):', error);
  }
}

/**
 * Log super admin configuration status (synchronous version for backward compatibility)
 * This should be called during application startup
 */
export function logSuperAdminConfigStatus(): void {
  try {
    const validation = validateSuperAdminConfig();
    const config = getSuperAdminConfig();

    console.log('Super Admin Configuration Status:');
    console.log(`  Environment Configured: ${config.isEnvironmentConfigured}`);
    console.log(`  Total Emails: ${config.emails.length}`);
    console.log(`  Database Driven: ${config.isDatabaseDriven}`);
    console.log(`  Valid: ${validation.isValid}`);

    if (validation.warnings.length > 0) {
      console.warn('Super Admin Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    if (validation.errors.length > 0) {
      console.error('Super Admin Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
    }

    // In development, show configured emails (masked)
    if (process.env.NODE_ENV === 'development') {
      console.log('Configured Super Admin Emails (masked):');
      config.emails.forEach(email => {
        const [local, domain] = email.split('@');
        const maskedLocal = local.length > 2 ? `${local[0]}***${local[local.length - 1]}` : '***';
        console.log(`  - ${maskedLocal}@${domain}`);
      });
    }
  } catch (error) {
    console.error('Failed to log super admin configuration status:', error);
  }
}

// Legacy compatibility exports (to be removed after migration)
// These are temporary to maintain compatibility during the migration
export const SUPER_ADMIN_EMAILS = getSuperAdminEmails();
export type SuperAdminEmail = string;
