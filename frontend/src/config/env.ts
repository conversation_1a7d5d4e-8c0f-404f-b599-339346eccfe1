/**
 * Environment Configuration
 * 
 * Centralized configuration for environment variables used throughout the application.
 * Provides type-safe access to environment variables with validation.
 */

export interface EnvironmentConfig {
  // MCP Rules Engine Configuration
  mcpRulesBase: string;
  mcpRulesBaseStaging?: string;
  featureMcpRulesEngine: boolean;

  // Supabase Configuration
  supabaseUrl: string;
  supabaseAnonKey: string;

  // Google Cloud Configuration
  googleCloudProject: string;
  mcpProject: string;
  tenantProject: string;

  // Feature Flags
  useSupabaseAuth: boolean;

  // Security Configuration
  superAdminEmails: string[];
}

/**
 * Validates and returns the environment configuration
 * Provides fallback values for build time when some variables may not be available
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const mcpRulesBase = process.env.MCP_RULES_BASE;

  // During build time, MCP_RULES_BASE may not be available, so provide a fallback
  const isBuildTime = process.env.NODE_ENV === 'production' && !process.env.VERCEL_ENV;

  if (!mcpRulesBase) {
    if (isBuildTime) {
      console.warn('MCP_RULES_BASE not set during build time. Using fallback value.');
    } else {
      throw new Error('MCP_RULES_BASE environment variable is required but not set');
    }
  }

  // Use fallback for build time or validate the actual value
  const finalMcpRulesBase = mcpRulesBase || 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev';

  // Validate that MCP_RULES_BASE is using the correct API Gateway format (skip during build)
  if (mcpRulesBase && !mcpRulesBase.includes('uc.gateway.dev')) {
    throw new Error(`MCP_RULES_BASE must use API Gateway host (uc.gateway.dev), got: ${mcpRulesBase}`);
  }
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required but not set');
  }
  
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!supabaseAnonKey) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required but not set');
  }
  
  // Parse super admin emails from environment variable
  const superAdminEmailsEnv = process.env.SUPER_ADMIN_EMAILS;
  let superAdminEmails: string[] = [];

  if (superAdminEmailsEnv) {
    try {
      // Support both comma-separated and JSON array formats
      if (superAdminEmailsEnv.startsWith('[')) {
        superAdminEmails = JSON.parse(superAdminEmailsEnv);
      } else {
        superAdminEmails = superAdminEmailsEnv.split(',').map(email => email.trim()).filter(Boolean);
      }

      // Validate email formats
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = superAdminEmails.filter(email => !emailRegex.test(email));
      if (invalidEmails.length > 0) {
        throw new Error(`Invalid super admin email format(s): ${invalidEmails.join(', ')}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse SUPER_ADMIN_EMAILS environment variable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  } else {
    console.warn('SUPER_ADMIN_EMAILS environment variable not set. Super admin functionality will be limited to database-driven configuration.');
  }

  return {
    // MCP Rules Engine Configuration
    mcpRulesBase: finalMcpRulesBase,
    mcpRulesBaseStaging: process.env.MCP_RULES_BASE_STG,
    featureMcpRulesEngine: process.env.FEATURE_MCP_RULES_ENGINE === 'true',

    // Supabase Configuration
    supabaseUrl,
    supabaseAnonKey,

    // Google Cloud Configuration
    googleCloudProject: process.env.GOOGLE_CLOUD_PROJECT || 'new-texas-laws',
    mcpProject: process.env.MCP_PROJECT || 'texas-laws-personalinjury',
    tenantProject: process.env.TENANT_PROJECT || 'new-texas-laws',

    // Feature Flags
    useSupabaseAuth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true',

    // Security Configuration
    superAdminEmails,
  };
}

/**
 * Environment configuration instance
 * Use this throughout the application for consistent environment variable access
 *
 * Note: This is lazily evaluated to avoid build-time errors when environment
 * variables are not available during static generation.
 */
let _env: EnvironmentConfig | null = null;

export const env = new Proxy({} as EnvironmentConfig, {
  get(target, prop) {
    if (!_env) {
      _env = getEnvironmentConfig();
    }
    return _env[prop as keyof EnvironmentConfig];
  }
});

/**
 * Development helper to check if we're in development mode
 */
export const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Production helper to check if we're in production mode
 */
export const isProduction = process.env.NODE_ENV === 'production';

/**
 * Staging helper to check if we're in staging mode
 */
export const isStaging = process.env.VERCEL_ENV === 'preview' || process.env.NODE_ENV === 'development';

/**
 * Get the appropriate MCP Rules Base URL based on environment
 * Provides fallback values to prevent runtime errors
 */
export function getMcpRulesBaseUrl(): string {
  try {
    const config = getEnvironmentConfig();

    if (isStaging && config.mcpRulesBaseStaging) {
      return config.mcpRulesBaseStaging;
    }

    return config.mcpRulesBase;
  } catch (error) {
    console.warn('Failed to get MCP Rules Base URL from config, using fallback:', error);
    // Fallback URL for when configuration fails
    return 'https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev';
  }
}

/**
 * Safe environment configuration getter
 * Returns null if configuration cannot be loaded (useful for optional features)
 */
export function getSafeEnvironmentConfig(): EnvironmentConfig | null {
  try {
    return getEnvironmentConfig();
  } catch (error) {
    console.warn('Failed to load environment configuration:', error);
    return null;
  }
}
