/**
 * Security Event Logging API
 * 
 * This endpoint receives security events from middleware and other security
 * components and logs them to the database for monitoring and analysis.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

interface SecurityEvent {
  timestamp: string;
  eventType: string;
  ip: string;
  userAgent: string | null;
  url: string;
  method: string;
  headers: Record<string, string>;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * POST /api/security/log-event
 * 
 * Logs security events to the database for monitoring and analysis
 */
export async function POST(request: NextRequest) {
  try {
    const securityEvent: SecurityEvent = await request.json();

    // Validate required fields
    if (!securityEvent.eventType || !securityEvent.timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields: eventType, timestamp' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = createClient();

    // Insert security event into database
    const { error } = await supabase
      .from('security_events')
      .insert({
        event_type: securityEvent.eventType,
        severity: securityEvent.severity,
        ip_address: securityEvent.ip,
        user_agent: securityEvent.userAgent,
        request_url: securityEvent.url,
        request_method: securityEvent.method,
        request_headers: securityEvent.headers,
        event_details: securityEvent.details,
        created_at: securityEvent.timestamp
      });

    if (error) {
      console.error('Failed to log security event:', error);
      return NextResponse.json(
        { error: 'Failed to log security event' },
        { status: 500 }
      );
    }

    // For critical events, trigger immediate alerts
    if (securityEvent.severity === 'critical') {
      await triggerSecurityAlert(securityEvent);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Security event logging error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Trigger immediate security alerts for critical events
 */
async function triggerSecurityAlert(event: SecurityEvent): Promise<void> {
  try {
    // In production, this would integrate with alerting systems like:
    // - Slack notifications
    // - Email alerts
    // - PagerDuty
    // - Security monitoring dashboards

    console.error('🚨 CRITICAL SECURITY EVENT:', {
      type: event.eventType,
      ip: event.ip,
      url: event.url,
      timestamp: event.timestamp,
      details: event.details
    });

    // For now, we'll just log to console
    // In production, implement actual alerting mechanism
    
  } catch (error) {
    console.error('Failed to trigger security alert:', error);
  }
}

/**
 * GET /api/security/log-event
 * 
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({ 
    status: 'Security event logging service is operational',
    timestamp: new Date().toISOString()
  });
}
