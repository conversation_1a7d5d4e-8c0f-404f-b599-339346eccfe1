'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { toast } from 'sonner'

import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MultiPracticeIntake } from "@/components/intake/multi-practice-intake"
import { InfoIcon, ShieldIcon, ClockIcon } from "lucide-react"

export default function SubmitMatterPage() {
  const router = useRouter()

  const handleIntakeComplete = (result: any) => {
    const displayLabel = result.matter?.display_label || 'matter'
    const practiceArea = result.matter?.practice_area?.replace('_', ' ') || 'legal'
    
    toast.success(`Your ${displayLabel.toLowerCase()} has been submitted successfully!`)
    
    // Show completion message with next steps
    toast.info(`Our team will review your ${practiceArea} ${displayLabel.toLowerCase()} and contact you within 24-48 hours.`)
    
    // Navigate back to client portal
    router.push('/client-portal')
  }

  return (
    <div className="container max-w-5xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Submit Your Legal Matter</h1>
          <p className="text-muted-foreground mt-1">
            Our AI assistant will guide you through submitting your case information
          </p>
        </div>
        <Link href="/client-portal">
          <Button variant="outline">← Back to Portal</Button>
        </Link>
      </div>

      {/* Important Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            <strong>Free Consultation</strong><br />
            Initial case review is always free with no obligation.
          </AlertDescription>
        </Alert>
        
        <Alert>
          <ShieldIcon className="h-4 w-4" />
          <AlertDescription>
            <strong>Confidential</strong><br />
            All information you provide is protected by attorney-client privilege.
          </AlertDescription>
        </Alert>
        
        <Alert>
          <ClockIcon className="h-4 w-4" />
          <AlertDescription>
            <strong>Quick Response</strong><br />
            We'll review your case and contact you within 24-48 hours.
          </AlertDescription>
        </Alert>
      </div>

      {/* Main Intake Interface */}
      <MultiPracticeIntake 
        mode="client" 
        onComplete={handleIntakeComplete}
      />

      {/* Practice Areas Information */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="text-lg">We Handle These Types of Cases</CardTitle>
          <CardDescription>
            Our experienced attorneys specialize in multiple practice areas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-primary">Personal Injury</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Car accidents and vehicle collisions</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Slip and fall accidents</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Medical malpractice</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Product liability</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Workplace injuries</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-primary">Family Law</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Divorce and separation</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Child custody and visitation</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Child support</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Adoption</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>Domestic violence protection</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-primary">Criminal Defense</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                  <span>DUI/DWI charges</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                  <span>Misdemeanor offenses</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                  <span>Felony charges</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                  <span>Traffic violations</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                  <span>White collar crimes</span>
                </div>
              </div>
              <Alert className="mt-3">
                <ClockIcon className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  <strong>Time Sensitive:</strong> Criminal cases require immediate attention. 
                  Contact us immediately if you have an upcoming court date.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Need Immediate Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Emergency Contact</h4>
              <p className="text-sm text-muted-foreground mb-2">
                For urgent criminal matters or time-sensitive cases:
              </p>
              <Button variant="destructive" size="sm">
                Call (*************
              </Button>
            </div>
            <div>
              <h4 className="font-semibold mb-2">General Inquiries</h4>
              <p className="text-sm text-muted-foreground mb-2">
                For general questions or non-urgent matters:
              </p>
              <Button variant="outline" size="sm">
                Email <EMAIL>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
