'use server'

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/lib/supabase/database.types'
import { isSuperAdminEmail } from '@/lib/auth/constants'
import { logSecurityEvent, SecurityEventType } from '@/lib/middleware/security-utils'

/**
 * Server action to handle superadmin password reset requests
 * 
 * This function validates that the email belongs to a verified superadmin
 * before sending the password reset email, providing an additional layer
 * of security for superadmin accounts.
 */
export async function resetSuperadminPasswordAction(
  formData: FormData
): Promise<{ error: string } | { success: string }> {
  const email = formData.get('email') as string

  // Validate input
  if (!email) {
    return { error: 'Email address is required.' }
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return { error: 'Please enter a valid email address.' }
  }

  try {
    // SECURITY CHECK: Verify the email is a valid superadmin email
    const isValidSuperAdmin = await isSuperAdminEmail(email)
    
    if (!isValidSuperAdmin) {
      console.warn(`SUPERADMIN PASSWORD RESET: Unauthorized attempt for ${email}`)
      
      // Log security event for unauthorized password reset attempt
      try {
        // Create a mock request object for logging
        const mockRequest = {
          url: '/loginadmin/forgot-password',
          method: 'POST',
          headers: new Map([
            ['user-agent', 'Server Action'],
            ['x-forwarded-for', 'server']
          ]),
          nextUrl: { pathname: '/loginadmin/forgot-password' }
        } as any

        await logSecurityEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          mockRequest,
          {
            attemptedEmail: email,
            action: 'superadmin_password_reset',
            reason: 'Email not in superadmin list',
            timestamp: new Date().toISOString()
          }
        )
      } catch (logError) {
        console.error('Failed to log security event:', logError)
      }

      // Return generic error to prevent email enumeration
      return { 
        error: 'If this email is associated with a superadmin account, you will receive password reset instructions.' 
      }
    }

    // Get the cookie store
    const cookieStore = await cookies()

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options })
            } catch (error) {
              // Ignore cookie setting errors in server actions
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: '', ...options })
            } catch (error) {
              // Ignore cookie removal errors in server actions
            }
          },
        },
      }
    )

    console.log(`SUPERADMIN PASSWORD RESET: Attempting reset for verified superadmin ${email}`)

    // Send password reset email with superadmin-specific redirect
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/loginadmin/update-password`,
    })

    if (resetError) {
      console.error('SUPERADMIN PASSWORD RESET ERROR:', resetError.message)
      
      // Log the error for monitoring
      try {
        const mockRequest = {
          url: '/loginadmin/forgot-password',
          method: 'POST',
          headers: new Map([
            ['user-agent', 'Server Action'],
            ['x-forwarded-for', 'server']
          ]),
          nextUrl: { pathname: '/loginadmin/forgot-password' }
        } as any

        await logSecurityEvent(
          SecurityEventType.AUTHENTICATION_FAILURE,
          mockRequest,
          {
            email,
            action: 'superadmin_password_reset',
            error: resetError.message,
            timestamp: new Date().toISOString()
          }
        )
      } catch (logError) {
        console.error('Failed to log security event:', logError)
      }

      // Handle specific Supabase errors
      if (resetError.message.includes('rate limit')) {
        return { 
          error: 'Too many password reset attempts. Please wait a few minutes before trying again.' 
        }
      } else if (resetError.message.includes('not found')) {
        // User doesn't exist in Supabase, but we don't want to reveal this
        return { 
          success: 'If this email is associated with a superadmin account, you will receive password reset instructions within a few minutes.' 
        }
      } else {
        return { 
          error: 'Unable to send password reset email. Please try again later or contact support.' 
        }
      }
    }

    console.log(`SUPERADMIN PASSWORD RESET: Reset email sent successfully to ${email}`)

    // Log successful password reset request
    try {
      const mockRequest = {
        url: '/loginadmin/forgot-password',
        method: 'POST',
        headers: new Map([
          ['user-agent', 'Server Action'],
          ['x-forwarded-for', 'server']
        ]),
        nextUrl: { pathname: '/loginadmin/forgot-password' }
      } as any

      await logSecurityEvent(
        SecurityEventType.AUTHENTICATION_FAILURE, // Using this as closest match for password reset
        mockRequest,
        {
          email,
          action: 'superadmin_password_reset_success',
          timestamp: new Date().toISOString()
        }
      )
    } catch (logError) {
      console.error('Failed to log security event:', logError)
    }

    return { 
      success: 'Password reset instructions have been sent to your email address. Please check your inbox and follow the instructions to reset your password.' 
    }

  } catch (error) {
    console.error('SUPERADMIN PASSWORD RESET: Unexpected error:', error)
    
    // Log unexpected error
    try {
      const mockRequest = {
        url: '/loginadmin/forgot-password',
        method: 'POST',
        headers: new Map([
          ['user-agent', 'Server Action'],
          ['x-forwarded-for', 'server']
        ]),
        nextUrl: { pathname: '/loginadmin/forgot-password' }
      } as any

      await logSecurityEvent(
        SecurityEventType.SUSPICIOUS_REQUEST,
        mockRequest,
        {
          email,
          action: 'superadmin_password_reset_error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      )
    } catch (logError) {
      console.error('Failed to log security event:', logError)
    }

    return { 
      error: 'An unexpected error occurred. Please try again later or contact support.' 
    }
  }
}
