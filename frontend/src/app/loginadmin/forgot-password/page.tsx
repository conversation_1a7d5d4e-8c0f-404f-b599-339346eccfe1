'use client'

import { useState, useTransition } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Shield, AlertTriangle, ArrowLeft, Mail, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import { resetSuperadminPasswordAction } from './actions'

export default function SuperadminForgotPasswordPage() {
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isPending, startTransition] = useTransition()
  const [email, setEmail] = useState('')

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      setError(null)
      setSuccess(null)
      
      const result = await resetSuperadminPasswordAction(formData)
      
      if (result && typeof result === 'object' && 'error' in result) {
        console.error("Superadmin password reset error:", result.error)
        setError(result.error)
      } else if (result && typeof result === 'object' && 'success' in result) {
        setSuccess(result.success)
        setEmail(formData.get('email') as string)
      }
    })
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-md">
        <div className="mb-8 flex justify-center">
          <div className="flex items-center space-x-2">
            <Shield className="h-8 w-8 text-primary" />
            <h1 className="text-2xl font-bold">Superadmin Portal</h1>
          </div>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Reset Password</CardTitle>
            <CardDescription className="text-center">
              {success 
                ? "Check your email for reset instructions"
                : "Enter your superadmin email to reset your password"
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {success ? (
              // Success state
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <Alert className="border-green-200 bg-green-50">
                  <Mail className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">Email Sent Successfully</AlertTitle>
                  <AlertDescription className="text-green-700">
                    {success}
                  </AlertDescription>
                </Alert>
                <div className="text-sm text-muted-foreground">
                  <p>We've sent password reset instructions to:</p>
                  <p className="font-medium text-foreground mt-1">{email}</p>
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>If you don't receive the email within a few minutes:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-left">
                    <li>Check your spam/junk folder</li>
                    <li>Verify the email address is correct</li>
                    <li>Contact your system administrator</li>
                  </ul>
                </div>
              </div>
            ) : (
              // Form state
              <>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Reset Failed</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                
                <form onSubmit={(e) => {
                  e.preventDefault()
                  const formData = new FormData(e.target as HTMLFormElement)
                  handleSubmit(formData)
                }}>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="email">Superadmin Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                        disabled={isPending}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground">
                        Only verified superadmin email addresses can reset passwords
                      </div>
                    </div>
                    
                    <Button type="submit" className="w-full" disabled={isPending}>
                      {isPending ? (
                        <>
                          <Mail className="mr-2 h-4 w-4 animate-spin" />
                          Sending Reset Email...
                        </>
                      ) : (
                        <>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Reset Email
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </>
            )}
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-4">
            <div className="flex items-center justify-center space-x-4 text-sm">
              <Link 
                href="/loginadmin" 
                className="flex items-center text-primary hover:underline"
              >
                <ArrowLeft className="mr-1 h-4 w-4" />
                Back to Login
              </Link>
              {success && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setSuccess(null)
                    setError(null)
                    setEmail('')
                  }}
                >
                  Send Another Email
                </Button>
              )}
            </div>
            
            <div className="text-xs text-center text-muted-foreground">
              <p>This portal is restricted to authorized administrators only.</p>
              <p className="mt-1">
                Password reset emails are only sent to verified superadmin addresses.
              </p>
            </div>
            
            <div className="text-sm text-center">
              <Link href="/login" className="text-primary hover:underline">
                Return to regular login
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
