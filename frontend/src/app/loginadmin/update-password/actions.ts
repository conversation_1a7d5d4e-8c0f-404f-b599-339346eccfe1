'use server'

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/lib/supabase/database.types'
import { isSuperAdminEmail } from '@/lib/auth/constants'
import { logSecurityEvent, SecurityEventType } from '@/lib/middleware/security-utils'

/**
 * Server action to handle superadmin password updates
 * 
 * This function validates the access token and updates the password
 * for verified superadmin users only.
 */
export async function updateSuperadminPasswordAction(
  formData: FormData
): Promise<{ error: string } | { success: string }> {
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const accessToken = formData.get('access_token') as string

  // Validate input
  if (!password || !confirmPassword) {
    return { error: 'Password and confirmation are required.' }
  }

  if (!accessToken) {
    return { error: 'Invalid or expired password reset link.' }
  }

  if (password !== confirmPassword) {
    return { error: 'Passwords do not match.' }
  }

  // Validate password strength
  const passwordValidation = validatePasswordStrength(password)
  if (!passwordValidation.isValid) {
    return { error: passwordValidation.message }
  }

  try {
    // Get the cookie store
    const cookieStore = await cookies()

    // Create Supabase client with the access token
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options })
            } catch (error) {
              // Ignore cookie setting errors in server actions
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value: '', ...options })
            } catch (error) {
              // Ignore cookie removal errors in server actions
            }
          },
        },
      }
    )

    // Set the session with the access token
    const { data: sessionData, error: sessionError } = await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: '', // Not needed for password update
    })

    if (sessionError || !sessionData.user) {
      console.error('SUPERADMIN PASSWORD UPDATE: Invalid session:', sessionError?.message)
      
      // Log security event for invalid session
      try {
        const mockRequest = {
          url: '/loginadmin/update-password',
          method: 'POST',
          headers: new Map([
            ['user-agent', 'Server Action'],
            ['x-forwarded-for', 'server']
          ]),
          nextUrl: { pathname: '/loginadmin/update-password' }
        } as any

        await logSecurityEvent(
          SecurityEventType.INVALID_SESSION,
          mockRequest,
          {
            action: 'superadmin_password_update',
            error: sessionError?.message || 'No user in session',
            timestamp: new Date().toISOString()
          }
        )
      } catch (logError) {
        console.error('Failed to log security event:', logError)
      }

      return { error: 'Invalid or expired password reset link. Please request a new password reset.' }
    }

    const user = sessionData.user
    const userEmail = user.email

    if (!userEmail) {
      return { error: 'Unable to verify user email. Please request a new password reset.' }
    }

    // SECURITY CHECK: Verify the user is a valid superadmin
    const isValidSuperAdmin = await isSuperAdminEmail(userEmail)
    
    if (!isValidSuperAdmin) {
      console.warn(`SUPERADMIN PASSWORD UPDATE: Unauthorized attempt for ${userEmail}`)
      
      // Log security event for unauthorized password update attempt
      try {
        const mockRequest = {
          url: '/loginadmin/update-password',
          method: 'POST',
          headers: new Map([
            ['user-agent', 'Server Action'],
            ['x-forwarded-for', 'server']
          ]),
          nextUrl: { pathname: '/loginadmin/update-password' }
        } as any

        await logSecurityEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          mockRequest,
          {
            attemptedEmail: userEmail,
            userId: user.id,
            action: 'superadmin_password_update',
            reason: 'Email not in superadmin list',
            timestamp: new Date().toISOString()
          }
        )
      } catch (logError) {
        console.error('Failed to log security event:', logError)
      }

      return { error: 'Unauthorized access. This action is restricted to verified superadmins.' }
    }

    console.log(`SUPERADMIN PASSWORD UPDATE: Updating password for verified superadmin ${userEmail}`)

    // Update the user's password
    const { error: updateError } = await supabase.auth.updateUser({
      password: password
    })

    if (updateError) {
      console.error('SUPERADMIN PASSWORD UPDATE ERROR:', updateError.message)
      
      // Log the error for monitoring
      try {
        const mockRequest = {
          url: '/loginadmin/update-password',
          method: 'POST',
          headers: new Map([
            ['user-agent', 'Server Action'],
            ['x-forwarded-for', 'server']
          ]),
          nextUrl: { pathname: '/loginadmin/update-password' }
        } as any

        await logSecurityEvent(
          SecurityEventType.AUTHENTICATION_FAILURE,
          mockRequest,
          {
            email: userEmail,
            userId: user.id,
            action: 'superadmin_password_update',
            error: updateError.message,
            timestamp: new Date().toISOString()
          }
        )
      } catch (logError) {
        console.error('Failed to log security event:', logError)
      }

      // Handle specific Supabase errors
      if (updateError.message.includes('weak')) {
        return { error: 'Password is too weak. Please choose a stronger password.' }
      } else if (updateError.message.includes('same')) {
        return { error: 'New password must be different from your current password.' }
      } else {
        return { error: 'Unable to update password. Please try again later.' }
      }
    }

    console.log(`SUPERADMIN PASSWORD UPDATE: Password updated successfully for ${userEmail}`)

    // Log successful password update
    try {
      const mockRequest = {
        url: '/loginadmin/update-password',
        method: 'POST',
        headers: new Map([
          ['user-agent', 'Server Action'],
          ['x-forwarded-for', 'server']
        ]),
        nextUrl: { pathname: '/loginadmin/update-password' }
      } as any

      await logSecurityEvent(
        SecurityEventType.AUTHENTICATION_FAILURE, // Using this as closest match for password update
        mockRequest,
        {
          email: userEmail,
          userId: user.id,
          action: 'superadmin_password_update_success',
          timestamp: new Date().toISOString()
        }
      )
    } catch (logError) {
      console.error('Failed to log security event:', logError)
    }

    // Sign out the user to force re-authentication with new password
    await supabase.auth.signOut()

    return { 
      success: 'Your password has been updated successfully. You can now log in with your new password.' 
    }

  } catch (error) {
    console.error('SUPERADMIN PASSWORD UPDATE: Unexpected error:', error)
    
    // Log unexpected error
    try {
      const mockRequest = {
        url: '/loginadmin/update-password',
        method: 'POST',
        headers: new Map([
          ['user-agent', 'Server Action'],
          ['x-forwarded-for', 'server']
        ]),
        nextUrl: { pathname: '/loginadmin/update-password' }
      } as any

      await logSecurityEvent(
        SecurityEventType.SUSPICIOUS_REQUEST,
        mockRequest,
        {
          action: 'superadmin_password_update_error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      )
    } catch (logError) {
      console.error('Failed to log security event:', logError)
    }

    return { 
      error: 'An unexpected error occurred. Please try again later or contact support.' 
    }
  }
}

/**
 * Validate password strength for superadmin accounts
 */
function validatePasswordStrength(password: string): { isValid: boolean; message: string } {
  if (password.length < 8) {
    return { isValid: false, message: 'Password must be at least 8 characters long.' }
  }

  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters long.' }
  }

  // Check for character variety
  const hasLowercase = /[a-z]/.test(password)
  const hasUppercase = /[A-Z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChars = /[^a-zA-Z0-9]/.test(password)

  const varietyCount = [hasLowercase, hasUppercase, hasNumbers, hasSpecialChars].filter(Boolean).length

  if (varietyCount < 3) {
    return { 
      isValid: false, 
      message: 'Password must contain at least 3 of the following: lowercase letters, uppercase letters, numbers, and special characters.' 
    }
  }

  // Check for common weak patterns
  if (/(.)\1{2,}/.test(password)) {
    return { isValid: false, message: 'Password cannot contain repeated characters.' }
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    return { isValid: false, message: 'Password cannot contain common patterns or words.' }
  }

  return { isValid: true, message: 'Password is strong.' }
}
