'use client'

import { useState, useEffect, useTransition } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Shield, AlertTriangle, CheckCircle, Eye, EyeOff, Lock } from 'lucide-react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { updateSuperadminPasswordAction } from './actions'

export default function SuperadminUpdatePasswordPage() {
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isPending, startTransition] = useTransition()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number
    feedback: string[]
  }>({ score: 0, feedback: [] })

  const router = useRouter()
  const searchParams = useSearchParams()

  // Check for access token and type in URL parameters
  useEffect(() => {
    const accessToken = searchParams.get('access_token')
    const type = searchParams.get('type')
    
    if (!accessToken || type !== 'recovery') {
      setError('Invalid or expired password reset link. Please request a new password reset.')
    }
  }, [searchParams])

  // Password strength validation
  useEffect(() => {
    if (!password) {
      setPasswordStrength({ score: 0, feedback: [] })
      return
    }

    const feedback: string[] = []
    let score = 0

    // Length check
    if (password.length >= 12) {
      score += 2
    } else if (password.length >= 8) {
      score += 1
      feedback.push('Consider using at least 12 characters for better security')
    } else {
      feedback.push('Password must be at least 8 characters long')
    }

    // Character variety checks
    if (/[a-z]/.test(password)) score += 1
    else feedback.push('Include lowercase letters')

    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('Include uppercase letters')

    if (/\d/.test(password)) score += 1
    else feedback.push('Include numbers')

    if (/[^a-zA-Z0-9]/.test(password)) score += 1
    else feedback.push('Include special characters (!@#$%^&*)')

    // Common patterns check
    if (/(.)\1{2,}/.test(password)) {
      score -= 1
      feedback.push('Avoid repeating characters')
    }

    setPasswordStrength({ score: Math.max(0, score), feedback })
  }, [password])

  const getPasswordStrengthColor = (score: number) => {
    if (score <= 2) return 'bg-red-500'
    if (score <= 4) return 'bg-yellow-500'
    if (score <= 6) return 'bg-green-500'
    return 'bg-green-600'
  }

  const getPasswordStrengthText = (score: number) => {
    if (score <= 2) return 'Weak'
    if (score <= 4) return 'Fair'
    if (score <= 6) return 'Good'
    return 'Strong'
  }

  const handleSubmit = async (formData: FormData) => {
    // Client-side validation
    if (password !== confirmPassword) {
      setError('Passwords do not match.')
      return
    }

    if (passwordStrength.score < 4) {
      setError('Password is too weak. Please choose a stronger password.')
      return
    }

    startTransition(async () => {
      setError(null)
      setSuccess(null)
      
      const result = await updateSuperadminPasswordAction(formData)
      
      if (result && typeof result === 'object' && 'error' in result) {
        console.error("Superadmin password update error:", result.error)
        setError(result.error)
      } else if (result && typeof result === 'object' && 'success' in result) {
        setSuccess(result.success)
        // Redirect to login after successful password update
        setTimeout(() => {
          router.push('/loginadmin?message=password_updated')
        }, 3000)
      }
    })
  }

  const accessToken = searchParams.get('access_token')
  const type = searchParams.get('type')
  const isValidResetLink = accessToken && type === 'recovery'

  if (!isValidResetLink) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl text-center text-red-600">Invalid Reset Link</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Link Expired or Invalid</AlertTitle>
                <AlertDescription>
                  This password reset link is invalid or has expired. Please request a new password reset.
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Link href="/loginadmin/forgot-password" className="text-primary hover:underline">
                Request New Password Reset
              </Link>
            </CardFooter>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-md">
        <div className="mb-8 flex justify-center">
          <div className="flex items-center space-x-2">
            <Shield className="h-8 w-8 text-primary" />
            <h1 className="text-2xl font-bold">Superadmin Portal</h1>
          </div>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Update Password</CardTitle>
            <CardDescription className="text-center">
              {success 
                ? "Password updated successfully"
                : "Enter your new superadmin password"
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {success ? (
              // Success state
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">Password Updated</AlertTitle>
                  <AlertDescription className="text-green-700">
                    {success}
                  </AlertDescription>
                </Alert>
                <div className="text-sm text-muted-foreground">
                  <p>You will be redirected to the login page in a few seconds...</p>
                </div>
              </div>
            ) : (
              // Form state
              <>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Update Failed</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                
                <form onSubmit={(e) => {
                  e.preventDefault()
                  const formData = new FormData(e.target as HTMLFormElement)
                  handleSubmit(formData)
                }}>
                  <input type="hidden" name="access_token" value={accessToken || ''} />
                  
                  <div className="grid gap-4">
                    {/* New Password Field */}
                    <div className="grid gap-2">
                      <Label htmlFor="password">New Password</Label>
                      <div className="relative">
                        <Input
                          id="password"
                          name="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter new password"
                          required
                          disabled={isPending}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      
                      {/* Password Strength Indicator */}
                      {password && (
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength.score)}`}
                                style={{ width: `${(passwordStrength.score / 7) * 100}%` }}
                              />
                            </div>
                            <span className="text-xs font-medium">
                              {getPasswordStrengthText(passwordStrength.score)}
                            </span>
                          </div>
                          {passwordStrength.feedback.length > 0 && (
                            <ul className="text-xs text-muted-foreground space-y-1">
                              {passwordStrength.feedback.map((item, index) => (
                                <li key={index}>• {item}</li>
                              ))}
                            </ul>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Confirm Password Field */}
                    <div className="grid gap-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm new password"
                          required
                          disabled={isPending}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      {confirmPassword && password !== confirmPassword && (
                        <div className="text-xs text-red-500">
                          Passwords do not match
                        </div>
                      )}
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={isPending || passwordStrength.score < 4 || password !== confirmPassword}
                    >
                      {isPending ? (
                        <>
                          <Lock className="mr-2 h-4 w-4 animate-spin" />
                          Updating Password...
                        </>
                      ) : (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Update Password
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </>
            )}
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-xs text-center text-muted-foreground">
              <p>This portal is restricted to authorized administrators only.</p>
              <p className="mt-1">
                Your new password must be strong and unique.
              </p>
            </div>
            
            {!success && (
              <div className="text-sm text-center">
                <Link href="/loginadmin" className="text-primary hover:underline">
                  Back to Login
                </Link>
              </div>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
