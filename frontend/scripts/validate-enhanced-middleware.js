#!/usr/bin/env node

/**
 * Enhanced Middleware Validation Script
 * 
 * This script validates the enhanced route protection middleware implementation
 * by testing various security scenarios and ensuring proper functionality.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Validation tests
const validationTests = [
  {
    name: 'Enhanced Middleware File Structure',
    test: () => {
      const middlewarePath = path.join(__dirname, '../src/middleware.ts');
      const securityUtilsPath = path.join(__dirname, '../src/lib/middleware/security-utils.ts');
      const logEventApiPath = path.join(__dirname, '../src/app/api/security/log-event/route.ts');
      
      const results = [];
      
      // Check middleware file exists and has enhanced imports
      if (fs.existsSync(middlewarePath)) {
        const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
        
        if (middlewareContent.includes('addSecurityHeaders')) {
          results.push({ status: 'pass', message: 'Enhanced security headers import found' });
        } else {
          results.push({ status: 'fail', message: 'Missing addSecurityHeaders import' });
        }
        
        if (middlewareContent.includes('validateRequest')) {
          results.push({ status: 'pass', message: 'Request validation import found' });
        } else {
          results.push({ status: 'fail', message: 'Missing validateRequest import' });
        }
        
        if (middlewareContent.includes('logSecurityEvent')) {
          results.push({ status: 'pass', message: 'Security event logging import found' });
        } else {
          results.push({ status: 'fail', message: 'Missing logSecurityEvent import' });
        }
        
        if (middlewareContent.includes('detectSuspiciousActivity')) {
          results.push({ status: 'pass', message: 'Suspicious activity detection import found' });
        } else {
          results.push({ status: 'fail', message: 'Missing detectSuspiciousActivity import' });
        }
      } else {
        results.push({ status: 'fail', message: 'Middleware file not found' });
      }
      
      // Check security utils file exists
      if (fs.existsSync(securityUtilsPath)) {
        results.push({ status: 'pass', message: 'Security utilities file exists' });
        
        const securityUtilsContent = fs.readFileSync(securityUtilsPath, 'utf8');
        
        // Check for key security functions
        const requiredFunctions = [
          'addSecurityHeaders',
          'validateRequest', 
          'detectSuspiciousActivity',
          'logSecurityEvent'
        ];
        
        requiredFunctions.forEach(func => {
          if (securityUtilsContent.includes(`export function ${func}`) ||
              securityUtilsContent.includes(`export async function ${func}`)) {
            results.push({ status: 'pass', message: `${func} function implemented` });
          } else {
            results.push({ status: 'fail', message: `Missing ${func} function` });
          }
        });
      } else {
        results.push({ status: 'fail', message: 'Security utilities file not found' });
      }
      
      // Check log event API exists
      if (fs.existsSync(logEventApiPath)) {
        results.push({ status: 'pass', message: 'Security log event API exists' });
      } else {
        results.push({ status: 'fail', message: 'Security log event API not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Security Headers Configuration',
    test: () => {
      const securityUtilsPath = path.join(__dirname, '../src/lib/middleware/security-utils.ts');
      const results = [];
      
      if (fs.existsSync(securityUtilsPath)) {
        const content = fs.readFileSync(securityUtilsPath, 'utf8');
        
        // Check for essential security headers
        const requiredHeaders = [
          'Content-Security-Policy',
          'X-XSS-Protection',
          'X-Content-Type-Options',
          'X-Frame-Options',
          'Referrer-Policy',
          'Strict-Transport-Security'
        ];
        
        requiredHeaders.forEach(header => {
          if (content.includes(`'${header}'`)) {
            results.push({ status: 'pass', message: `${header} header configured` });
          } else {
            results.push({ status: 'fail', message: `Missing ${header} header` });
          }
        });
        
        // Check for CSP configuration
        if (content.includes("default-src 'self'")) {
          results.push({ status: 'pass', message: 'CSP default-src configured' });
        } else {
          results.push({ status: 'fail', message: 'Missing CSP default-src configuration' });
        }
        
        // Check for HSTS configuration
        if (content.includes('max-age=31536000')) {
          results.push({ status: 'pass', message: 'HSTS properly configured' });
        } else {
          results.push({ status: 'fail', message: 'HSTS not properly configured' });
        }
      } else {
        results.push({ status: 'fail', message: 'Security utilities file not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Request Validation Security',
    test: () => {
      const securityUtilsPath = path.join(__dirname, '../src/lib/middleware/security-utils.ts');
      const results = [];
      
      if (fs.existsSync(securityUtilsPath)) {
        const content = fs.readFileSync(securityUtilsPath, 'utf8');
        
        // Check for security validation patterns
        const securityChecks = [
          { pattern: 'MAX_REQUEST_SIZE', message: 'Request size validation' },
          { pattern: 'MAX_URL_LENGTH', message: 'URL length validation' },
          { pattern: 'SUSPICIOUS_USER_AGENTS', message: 'User agent validation' },
          { pattern: 'BLOCKED_EXTENSIONS', message: 'File extension blocking' },
          { pattern: '/\\.\\./', message: 'Directory traversal protection' },
          { pattern: '/<script/i', message: 'XSS protection' },
          { pattern: '/union.*select/i', message: 'SQL injection protection' }
        ];
        
        securityChecks.forEach(check => {
          if (content.includes(check.pattern)) {
            results.push({ status: 'pass', message: `${check.message} implemented` });
          } else {
            results.push({ status: 'fail', message: `Missing ${check.message}` });
          }
        });
      } else {
        results.push({ status: 'fail', message: 'Security utilities file not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Security Event Logging',
    test: () => {
      const securityUtilsPath = path.join(__dirname, '../src/lib/middleware/security-utils.ts');
      const logEventApiPath = path.join(__dirname, '../src/app/api/security/log-event/route.ts');
      const results = [];
      
      // Check security event types
      if (fs.existsSync(securityUtilsPath)) {
        const content = fs.readFileSync(securityUtilsPath, 'utf8');
        
        const eventTypes = [
          'AUTHENTICATION_FAILURE',
          'AUTHORIZATION_FAILURE',
          'SUSPICIOUS_REQUEST',
          'MALICIOUS_PAYLOAD',
          'UNAUTHORIZED_ACCESS'
        ];
        
        eventTypes.forEach(eventType => {
          if (content.includes(eventType)) {
            results.push({ status: 'pass', message: `${eventType} event type defined` });
          } else {
            results.push({ status: 'fail', message: `Missing ${eventType} event type` });
          }
        });
      }
      
      // Check API endpoint implementation
      if (fs.existsSync(logEventApiPath)) {
        const apiContent = fs.readFileSync(logEventApiPath, 'utf8');
        
        if (apiContent.includes('export async function POST')) {
          results.push({ status: 'pass', message: 'Security event logging API POST method implemented' });
        } else {
          results.push({ status: 'fail', message: 'Missing POST method in logging API' });
        }
        
        if (apiContent.includes('triggerSecurityAlert')) {
          results.push({ status: 'pass', message: 'Critical event alerting implemented' });
        } else {
          results.push({ status: 'fail', message: 'Missing critical event alerting' });
        }
      } else {
        results.push({ status: 'fail', message: 'Security log event API not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Database Migration',
    test: () => {
      const migrationPath = path.join(__dirname, '../../supabase/migrations/20250309_security_events_table.sql');
      const results = [];
      
      if (fs.existsSync(migrationPath)) {
        results.push({ status: 'pass', message: 'Security events database migration exists' });
        
        const migrationContent = fs.readFileSync(migrationPath, 'utf8');
        
        // Check for essential table structure
        if (migrationContent.includes('CREATE TABLE IF NOT EXISTS security.security_events')) {
          results.push({ status: 'pass', message: 'Security events table creation script found' });
        } else {
          results.push({ status: 'fail', message: 'Missing security events table creation' });
        }
        
        // Check for indexes
        if (migrationContent.includes('CREATE INDEX')) {
          results.push({ status: 'pass', message: 'Database indexes configured' });
        } else {
          results.push({ status: 'fail', message: 'Missing database indexes' });
        }
        
        // Check for RLS policies
        if (migrationContent.includes('ROW LEVEL SECURITY')) {
          results.push({ status: 'pass', message: 'Row Level Security configured' });
        } else {
          results.push({ status: 'fail', message: 'Missing Row Level Security' });
        }
      } else {
        results.push({ status: 'fail', message: 'Security events database migration not found' });
      }
      
      return results;
    }
  }
];

// Run validation tests
async function runValidation() {
  log('\n🔒 Enhanced Route Protection Middleware Validation', 'bold');
  log('=' .repeat(60), 'blue');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  
  for (const test of validationTests) {
    log(`\n📋 Testing: ${test.name}`, 'yellow');
    log('-'.repeat(40), 'blue');
    
    try {
      const results = test.test();
      
      results.forEach(result => {
        totalTests++;
        if (result.status === 'pass') {
          logSuccess(result.message);
          passedTests++;
        } else {
          logError(result.message);
          failedTests++;
        }
      });
    } catch (error) {
      logError(`Test failed with error: ${error.message}`);
      failedTests++;
      totalTests++;
    }
  }
  
  // Summary
  log('\n📊 Validation Summary', 'bold');
  log('=' .repeat(60), 'blue');
  log(`Total Tests: ${totalTests}`, 'blue');
  logSuccess(`Passed: ${passedTests}`);
  
  if (failedTests > 0) {
    logError(`Failed: ${failedTests}`);
  } else {
    logSuccess(`Failed: ${failedTests}`);
  }
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  log(`Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  if (successRate >= 90) {
    log('\n🎉 Enhanced middleware validation PASSED! Ready for deployment.', 'green');
  } else if (successRate >= 70) {
    log('\n⚠️  Enhanced middleware validation has some issues. Review failed tests.', 'yellow');
  } else {
    log('\n❌ Enhanced middleware validation FAILED. Critical issues need to be addressed.', 'red');
  }
  
  return {
    totalTests,
    passedTests,
    failedTests,
    successRate: parseFloat(successRate)
  };
}

// Run the validation if this script is executed directly
if (require.main === module) {
  runValidation().catch(error => {
    logError(`Validation script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runValidation };
