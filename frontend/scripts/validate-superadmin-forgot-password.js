#!/usr/bin/env node

/**
 * Superadmin Forgot Password Validation Script
 * 
 * This script validates the superadmin forgot password implementation
 * by testing file structure, functionality, and security measures.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Validation tests
const validationTests = [
  {
    name: 'File Structure Validation',
    test: () => {
      const results = [];
      
      const requiredFiles = [
        'frontend/src/app/loginadmin/forgot-password/page.tsx',
        'frontend/src/app/loginadmin/forgot-password/actions.ts',
        'frontend/src/app/loginadmin/update-password/page.tsx',
        'frontend/src/app/loginadmin/update-password/actions.ts'
      ];
      
      requiredFiles.forEach(filePath => {
        const fullPath = path.join(__dirname, '../..', filePath);
        if (fs.existsSync(fullPath)) {
          results.push({ status: 'pass', message: `${filePath} exists` });
        } else {
          results.push({ status: 'fail', message: `Missing ${filePath}` });
        }
      });
      
      return results;
    }
  },
  
  {
    name: 'Forgot Password Page Implementation',
    test: () => {
      const results = [];
      const forgotPasswordPath = path.join(__dirname, '../src/app/loginadmin/forgot-password/page.tsx');
      
      if (fs.existsSync(forgotPasswordPath)) {
        const content = fs.readFileSync(forgotPasswordPath, 'utf8');
        
        // Check for essential components
        const requiredElements = [
          { pattern: 'resetSuperadminPasswordAction', message: 'Server action import' },
          { pattern: 'useState', message: 'React state management' },
          { pattern: 'useTransition', message: 'React transition hook' },
          { pattern: 'Shield', message: 'Security icon' },
          { pattern: 'CheckCircle', message: 'Success icon' },
          { pattern: 'Mail', message: 'Email icon' },
          { pattern: 'AlertTriangle', message: 'Error icon' },
          { pattern: 'success &&', message: 'Success state handling' },
          { pattern: 'error &&', message: 'Error state handling' },
          { pattern: 'isPending', message: 'Loading state handling' },
          { pattern: 'formData', message: 'Form data handling' },
          { pattern: 'Superadmin Portal', message: 'Proper branding' },
          { pattern: 'verified superadmin', message: 'Security messaging' }
        ];
        
        requiredElements.forEach(element => {
          if (content.includes(element.pattern)) {
            results.push({ status: 'pass', message: `${element.message} implemented` });
          } else {
            results.push({ status: 'fail', message: `Missing ${element.message}` });
          }
        });
      } else {
        results.push({ status: 'fail', message: 'Forgot password page not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Update Password Page Implementation',
    test: () => {
      const results = [];
      const updatePasswordPath = path.join(__dirname, '../src/app/loginadmin/update-password/page.tsx');
      
      if (fs.existsSync(updatePasswordPath)) {
        const content = fs.readFileSync(updatePasswordPath, 'utf8');
        
        // Check for essential components
        const requiredElements = [
          { pattern: 'updateSuperadminPasswordAction', message: 'Server action import' },
          { pattern: 'useSearchParams', message: 'URL parameter handling' },
          { pattern: 'access_token', message: 'Access token validation' },
          { pattern: 'type === \'recovery\'', message: 'Recovery type validation' },
          { pattern: 'passwordStrength', message: 'Password strength validation' },
          { pattern: 'showPassword', message: 'Password visibility toggle' },
          { pattern: 'confirmPassword', message: 'Password confirmation' },
          { pattern: 'getPasswordStrengthColor', message: 'Password strength indicator' },
          { pattern: 'Eye', message: 'Password visibility icons' },
          { pattern: 'EyeOff', message: 'Password visibility icons' },
          { pattern: 'Lock', message: 'Security icon' },
          { pattern: 'Invalid Reset Link', message: 'Invalid link handling' },
          { pattern: 'router.push', message: 'Navigation after success' }
        ];
        
        requiredElements.forEach(element => {
          if (content.includes(element.pattern)) {
            results.push({ status: 'pass', message: `${element.message} implemented` });
          } else {
            results.push({ status: 'fail', message: `Missing ${element.message}` });
          }
        });
      } else {
        results.push({ status: 'fail', message: 'Update password page not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'Server Actions Security',
    test: () => {
      const results = [];
      
      // Check forgot password action
      const forgotPasswordActionPath = path.join(__dirname, '../src/app/loginadmin/forgot-password/actions.ts');
      if (fs.existsSync(forgotPasswordActionPath)) {
        const content = fs.readFileSync(forgotPasswordActionPath, 'utf8');
        
        const securityChecks = [
          { pattern: 'isSuperAdminEmail', message: 'Superadmin email validation' },
          { pattern: 'logSecurityEvent', message: 'Security event logging' },
          { pattern: 'UNAUTHORIZED_ACCESS', message: 'Unauthorized access logging' },
          { pattern: 'emailRegex', message: 'Email format validation' },
          { pattern: 'resetPasswordForEmail', message: 'Supabase password reset' },
          { pattern: '/loginadmin/update-password', message: 'Correct redirect URL' },
          { pattern: 'rate limit', message: 'Rate limiting error handling' },
          { pattern: 'generic error', message: 'Email enumeration prevention' }
        ];
        
        securityChecks.forEach(check => {
          if (content.includes(check.pattern)) {
            results.push({ status: 'pass', message: `Forgot password: ${check.message}` });
          } else {
            results.push({ status: 'fail', message: `Forgot password: Missing ${check.message}` });
          }
        });
      }
      
      // Check update password action
      const updatePasswordActionPath = path.join(__dirname, '../src/app/loginadmin/update-password/actions.ts');
      if (fs.existsSync(updatePasswordActionPath)) {
        const content = fs.readFileSync(updatePasswordActionPath, 'utf8');
        
        const securityChecks = [
          { pattern: 'validatePasswordStrength', message: 'Password strength validation' },
          { pattern: 'setSession', message: 'Session validation with access token' },
          { pattern: 'isSuperAdminEmail', message: 'Superadmin email verification' },
          { pattern: 'updateUser', message: 'Supabase password update' },
          { pattern: 'signOut', message: 'Force re-authentication' },
          { pattern: 'length < 8', message: 'Minimum password length' },
          { pattern: 'varietyCount', message: 'Character variety requirement' },
          { pattern: 'repeated characters', message: 'Repeated character prevention' },
          { pattern: 'common patterns', message: 'Common pattern prevention' }
        ];
        
        securityChecks.forEach(check => {
          if (content.includes(check.pattern)) {
            results.push({ status: 'pass', message: `Update password: ${check.message}` });
          } else {
            results.push({ status: 'fail', message: `Update password: Missing ${check.message}` });
          }
        });
      }
      
      return results;
    }
  },
  
  {
    name: 'Login Page Integration',
    test: () => {
      const results = [];
      const loginPagePath = path.join(__dirname, '../src/app/loginadmin/page.tsx');
      
      if (fs.existsSync(loginPagePath)) {
        const content = fs.readFileSync(loginPagePath, 'utf8');
        
        // Check for forgot password integration
        const integrationChecks = [
          { pattern: '/loginadmin/forgot-password', message: 'Forgot password link' },
          { pattern: 'Forgot Password?', message: 'Forgot password text' },
          { pattern: 'success &&', message: 'Success message handling' },
          { pattern: 'password_updated', message: 'Password update success message' },
          { pattern: 'CheckCircle', message: 'Success icon import' }
        ];
        
        integrationChecks.forEach(check => {
          if (content.includes(check.pattern)) {
            results.push({ status: 'pass', message: `Login page: ${check.message}` });
          } else {
            results.push({ status: 'fail', message: `Login page: Missing ${check.message}` });
          }
        });
      } else {
        results.push({ status: 'fail', message: 'Login admin page not found' });
      }
      
      return results;
    }
  },
  
  {
    name: 'TypeScript Compilation',
    test: () => {
      const results = [];
      
      // Check if TypeScript files are properly typed
      const tsFiles = [
        'frontend/src/app/loginadmin/forgot-password/page.tsx',
        'frontend/src/app/loginadmin/forgot-password/actions.ts',
        'frontend/src/app/loginadmin/update-password/page.tsx',
        'frontend/src/app/loginadmin/update-password/actions.ts'
      ];
      
      tsFiles.forEach(filePath => {
        const fullPath = path.join(__dirname, '../..', filePath);
        if (fs.existsSync(fullPath)) {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Check for proper TypeScript usage
          if (content.includes('FormData') && content.includes('useState')) {
            results.push({ status: 'pass', message: `${path.basename(filePath)}: Proper TypeScript types` });
          }
          
          if (content.includes("'use server'") || content.includes("'use client'")) {
            results.push({ status: 'pass', message: `${path.basename(filePath)}: Proper directive usage` });
          }
          
          // Check for no any types (good practice)
          if (!content.includes(': any')) {
            results.push({ status: 'pass', message: `${path.basename(filePath)}: No any types used` });
          } else {
            results.push({ status: 'warning', message: `${path.basename(filePath)}: Contains any types` });
          }
        }
      });
      
      return results;
    }
  }
];

// Run validation tests
async function runValidation() {
  log('\n🔐 Superadmin Forgot Password Implementation Validation', 'bold');
  log('=' .repeat(70), 'blue');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  let warningTests = 0;
  
  for (const test of validationTests) {
    log(`\n📋 Testing: ${test.name}`, 'yellow');
    log('-'.repeat(50), 'blue');
    
    try {
      const results = test.test();
      
      results.forEach(result => {
        totalTests++;
        if (result.status === 'pass') {
          logSuccess(result.message);
          passedTests++;
        } else if (result.status === 'warning') {
          logWarning(result.message);
          warningTests++;
        } else {
          logError(result.message);
          failedTests++;
        }
      });
    } catch (error) {
      logError(`Test failed with error: ${error.message}`);
      failedTests++;
      totalTests++;
    }
  }
  
  // Summary
  log('\n📊 Validation Summary', 'bold');
  log('=' .repeat(70), 'blue');
  log(`Total Tests: ${totalTests}`, 'blue');
  logSuccess(`Passed: ${passedTests}`);
  
  if (warningTests > 0) {
    logWarning(`Warnings: ${warningTests}`);
  }
  
  if (failedTests > 0) {
    logError(`Failed: ${failedTests}`);
  } else {
    logSuccess(`Failed: ${failedTests}`);
  }
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  log(`Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  if (successRate >= 95) {
    log('\n🎉 Superadmin forgot password validation PASSED! Ready for deployment.', 'green');
  } else if (successRate >= 85) {
    log('\n⚠️  Superadmin forgot password validation mostly passed. Review warnings.', 'yellow');
  } else {
    log('\n❌ Superadmin forgot password validation FAILED. Critical issues need to be addressed.', 'red');
  }
  
  return {
    totalTests,
    passedTests,
    failedTests,
    warningTests,
    successRate: parseFloat(successRate)
  };
}

// Run the validation if this script is executed directly
if (require.main === module) {
  runValidation().catch(error => {
    logError(`Validation script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runValidation };
