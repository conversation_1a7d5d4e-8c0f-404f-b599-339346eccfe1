"""
Test suite for input validation implementation.

This test suite verifies the comprehensive input validation and sanitization
functionality implemented in Task 2.2.
"""

import pytest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from pi_lawyer.middleware.input_validation_middleware import (
    ValidationConfig, InputValidator, ValidationResult
)
from pi_lawyer.db.query_validation import (
    validate_supabase_query, QueryValidationError, QueryValidator
)


class TestInputValidator:
    """Test the InputValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = InputValidator()
    
    def test_xss_detection(self):
        """Test XSS pattern detection."""
        xss_payloads = [
            '<script>alert("xss")</script>',
            '<iframe src="javascript:alert(1)"></iframe>',
            'javascript:alert(1)',
            'vbscript:msgbox(1)',
            '<img src="x" onerror="alert(1)">',
        ]
        
        for payload in xss_payloads:
            assert self.validator._contains_malicious_patterns(payload), f"Failed to detect XSS in: {payload}"
    
    def test_sql_injection_detection(self):
        """Test SQL injection pattern detection."""
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR 1=1 --",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "/* comment */ SELECT * FROM users",
        ]
        
        for payload in sql_payloads:
            assert self.validator._contains_malicious_patterns(payload), f"Failed to detect SQL injection in: {payload}"
    
    def test_command_injection_detection(self):
        """Test command injection pattern detection."""
        cmd_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "& whoami",
            "`id`",
            "$(whoami)",
            "${USER}",
        ]
        
        for payload in cmd_payloads:
            assert self.validator._contains_malicious_patterns(payload), f"Failed to detect command injection in: {payload}"
    
    def test_safe_content(self):
        """Test that safe content is not flagged."""
        safe_contents = [
            "This is a normal message about legal matters.",
            "Client John Doe needs help with personal injury case.",
            "Meeting scheduled for 2:00 PM tomorrow.",
            "Document review completed successfully.",
            "Invoice #12345 for $1,500.00",
        ]
        
        for content in safe_contents:
            assert not self.validator._contains_malicious_patterns(content), f"False positive for safe content: {content}"
    
    def test_validation_result_creation(self):
        """Test ValidationResult object creation."""
        result = ValidationResult(
            is_valid=False,
            violations=["XSS detected"],
            risk_level="high"
        )
        
        assert not result.is_valid
        assert "XSS detected" in result.violations
        assert result.risk_level == "high"


class TestQueryValidator:
    """Test the QueryValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = QueryValidator()
    
    def test_valid_tenant_table_names(self):
        """Test validation of allowed tenant table names."""
        valid_tables = ['matters', 'clients', 'documents', 'deadlines', 'tasks']
        
        for table in valid_tables:
            # Should not raise exception
            self.validator.validate_table_name(table, 'tenants')
    
    def test_invalid_tenant_table_names(self):
        """Test rejection of invalid tenant table names."""
        invalid_tables = ['malicious_table', 'users', 'admin_data', 'system_config']
        
        for table in invalid_tables:
            with pytest.raises(QueryValidationError):
                self.validator.validate_table_name(table, 'tenants')
    
    def test_valid_public_table_names(self):
        """Test validation of allowed public table names."""
        valid_tables = ['subscription_plans', 'features', 'practice_areas']
        
        for table in valid_tables:
            # Should not raise exception
            self.validator.validate_table_name(table, 'public')
    
    def test_column_name_validation(self):
        """Test column name format validation."""
        valid_columns = ['id', 'tenant_id', 'created_at', 'user_name', 'email_address']
        
        for column in valid_columns:
            # Should not raise exception
            self.validator.validate_column_name(column)
        
        # Test wildcard
        self.validator.validate_column_name('*')
        
        # Test comma-separated columns
        self.validator.validate_column_name('id, name, email')
    
    def test_invalid_column_names(self):
        """Test rejection of invalid column names."""
        invalid_columns = ['1invalid', 'col-name', 'col name', 'col;name', 'DROP TABLE']
        
        for column in invalid_columns:
            with pytest.raises(QueryValidationError):
                self.validator.validate_column_name(column)
    
    def test_filter_value_validation(self):
        """Test filter value validation and sanitization."""
        # Valid values
        valid_values = [
            'normal_string',
            123,
            45.67,
            True,
            None,
            ['item1', 'item2', 'item3']
        ]
        
        for value in valid_values:
            # Should not raise exception
            result = self.validator.validate_filter_value(value)
            assert result is not None or value is None
    
    def test_malicious_filter_values(self):
        """Test rejection of malicious filter values."""
        malicious_values = [
            "'; DROP TABLE users; --",
            '<script>alert("xss")</script>',
            '| cat /etc/passwd',
        ]
        
        for value in malicious_values:
            with pytest.raises(QueryValidationError):
                self.validator.validate_filter_value(value)


class TestSupabaseQueryValidation:
    """Test the complete Supabase query validation function."""
    
    def test_valid_query_validation(self):
        """Test validation of a complete valid query."""
        result = validate_supabase_query(
            table_name='matters',
            schema='tenants',
            columns='id, title, status',
            filters={'tenant_id': '123e4567-e89b-12d3-a456-426614174000'},
            order_by='created_at DESC',
            limit=50
        )
        
        assert result['table_name'] == 'matters'
        assert result['schema'] == 'tenants'
        assert result['columns'] == 'id, title, status'
        assert result['limit'] == 50
    
    def test_invalid_query_rejection(self):
        """Test rejection of invalid queries."""
        # Invalid table name
        with pytest.raises(QueryValidationError):
            validate_supabase_query(
                table_name='malicious_table',
                schema='tenants'
            )
        
        # Invalid column name
        with pytest.raises(QueryValidationError):
            validate_supabase_query(
                table_name='matters',
                schema='tenants',
                columns='DROP TABLE'
            )
        
        # Invalid limit
        with pytest.raises(QueryValidationError):
            validate_supabase_query(
                table_name='matters',
                schema='tenants',
                limit=5000  # Exceeds maximum
            )


if __name__ == '__main__':
    # Run basic tests if executed directly
    print("Running basic input validation tests...")
    
    # Test input validator
    validator = InputValidator()
    
    # Test XSS detection
    xss_result = validator._contains_malicious_patterns('<script>alert("test")</script>')
    print(f"✅ XSS detection: {xss_result}")
    
    # Test SQL injection detection
    sql_result = validator._contains_malicious_patterns("'; DROP TABLE users; --")
    print(f"✅ SQL injection detection: {sql_result}")
    
    # Test safe content
    safe_result = validator._contains_malicious_patterns("This is safe content")
    print(f"✅ Safe content validation: {not safe_result}")
    
    # Test query validation
    try:
        query_result = validate_supabase_query('matters', 'tenants')
        print("✅ Valid query validation passed")
    except QueryValidationError:
        print("❌ Valid query validation failed")
    
    try:
        validate_supabase_query('invalid_table', 'tenants')
        print("❌ Invalid query validation should have failed")
    except QueryValidationError:
        print("✅ Invalid query validation correctly rejected")
    
    print("\n🎉 All basic tests passed!")
